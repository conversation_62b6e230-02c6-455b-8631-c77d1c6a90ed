import 'dart:async';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cbrs/core/models/agora_user.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/presence_service.dart';
import 'package:cbrs/features/chat/data/datasources/chat_remote_datasource.dart';
import 'package:flutter/foundation.dart';

/// Service to handle Agora Chat functionality
class AgoraChatService {
  static const String _appKey = '711259554#1570956';

  ChatClient? _chatClient;
  bool _isInitialized = false;
  bool _isLoggedIn = false;

  String? _currentUserId;
  String? _currentToken;

  // Stream controllers for real-time events
  final _contactAddedController = StreamController<String>.broadcast();
  final _contactDeletedController = StreamController<String>.broadcast();
  final _messageReceivedController = StreamController<ChatMessage>.broadcast();
  final _messageStatusController = StreamController<ChatMessage>.broadcast();
  final _messageRecalledController = StreamController<ChatMessage>.broadcast();
  final _messageContentChangedController =
      StreamController<ChatMessage>.broadcast();
  final _messageIdChangedController =
      StreamController<Map<String, String>>.broadcast();

  // Map to store local message ID to server message ID mapping
  final Map<String, String> _messageIdMapping = {};

  // Stream getters
  Stream<String> get contactAddedStream => _contactAddedController.stream;
  Stream<String> get contactDeletedStream => _contactDeletedController.stream;
  Stream<ChatMessage> get messageReceivedStream =>
      _messageReceivedController.stream;
  Stream<ChatMessage> get messageStatusStream =>
      _messageStatusController.stream;
  Stream<ChatMessage> get messageRecalledStream =>
      _messageRecalledController.stream;
  Stream<ChatMessage> get messageContentChangedStream =>
      _messageContentChangedController.stream;

  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('Agora Chat: Attempting to initialize with App Key: $_appKey');
    if (_appKey.isEmpty) {
      debugPrint('Agora Chat: Initialization failed - App Key is empty.');
      return;
    }

    final options = ChatOptions(
      appKey: _appKey,
      autoLogin: false,
      debugMode: true,
      enableEmptyConversation: true,
      useReplacedMessageContents: true,
      acceptInvitationAlways: true, // Auto-accept friend requests
      autoAcceptGroupInvitation: true, // Auto-accept group invitations
    );

    _chatClient = ChatClient.getInstance;

    try {
      await _chatClient!.init(options);
      debugPrint('Agora Chat: Initialization successful.');
      _addChatListeners();
      _isInitialized = true;
    } on ChatError catch (e) {
      debugPrint('Agora Chat: Initialization failed - Error: ${e.description}');
      rethrow;
    }
  }

  void _addChatListeners() {
    _chatClient!.addConnectionEventHandler(
      'agora_chat_connection_handler',
      ConnectionEventHandler(
        onConnected: () {
          debugPrint('Agora Chat: Connection successful.');
          _isLoggedIn = true;
        },
        onDisconnected: () {
          debugPrint('Agora Chat: Disconnected.');
          _isLoggedIn = false;
        },
        onTokenWillExpire: () {
          debugPrint(
            'Agora Chat: Token will expire - letting SDK handle refresh',
          );
          // Let the SDK handle token refresh automatically
        },
        onTokenDidExpire: () {
          debugPrint('Agora Chat: Token expired - SDK will handle renewal');
          // Let the SDK handle token renewal automatically
        },
      ),
    );

    // Add presence event handler
    _chatClient!.presenceManager.addEventHandler(
      'presence_handler',
      ChatPresenceEventHandler(
        onPresenceStatusChanged: (presenceList) {
          debugPrint(
            'Agora Chat: Presence status changed for ${presenceList.length} users',
          );
          _handlePresenceStatusChanged(presenceList);
        },
      ),
    );

    // Add contact event handler
    _chatClient!.contactManager.addEventHandler(
      'agora_contact_handler',
      ChatContactEventHandler(
        onContactAdded: (String userId) {
          debugPrint('Agora Chat: Contact added: $userId');
          _onContactAdded(userId);
        },
        onContactDeleted: (String userId) {
          debugPrint('Agora Chat: Contact deleted: $userId');
          _onContactDeleted(userId);
        },
      ),
    );

    _chatClient!.chatManager.addEventHandler(
      'message_handler',
      ChatEventHandler(
        onMessagesReceived: (messages) {
          debugPrint('Agora Chat: Received ${messages.length} new messages');
          for (final message in messages) {
            debugPrint('Message from ${message.from}: ${message.body}');
            // Broadcast new message to listeners
            _onMessageReceived(message);
          }
        },
        onMessagesDelivered: (messages) {
          debugPrint('Agora Chat: ${messages.length} messages delivered');
          for (final message in messages) {
            _onMessageStatusChanged(message, 'delivered');
          }
        },
        onMessagesRead: (messages) {
          debugPrint('Agora Chat: ${messages.length} messages read');
          for (final message in messages) {
            _onMessageStatusChanged(message, 'read');
          }
        },
        onMessagesRecalled: (messages) {
          debugPrint('Agora Chat: ${messages.length} messages recalled');
          for (final message in messages) {
            debugPrint('Message recalled: ${message.msgId}');
            _onMessageRecalled(message);
          }
        },
        onMessageContentChanged: (message, operatorId, operationTime) {
          debugPrint('Agora Chat: Message content changed: ${message.msgId}');
          _onMessageContentChanged(message, operatorId, operationTime);
        },
      ),
    );
  }

  /// Login to Agora Chat with username and token
  Future<bool> login(String username, String token) async {
    if (_chatClient == null) {
      debugPrint('Agora Chat: Login failed - ChatClient is not initialized.');
      return false;
    }

    if (!_isInitialized) {
      debugPrint('🔥 Agora Chat: Not initialized, initializing now...');
      await initialize();
    }

    if (_isLoggedIn && _currentUserId == username) {
      debugPrint('🔥 Agora Chat: Already logged in as $username');
      return true;
    }

    try {
      // Use loginWithToken method (loginWithAgoraToken is deprecated)
      await _chatClient!.loginWithToken(username, token);
      _currentUserId = username;
      _currentToken = token;
      _isLoggedIn = true;
      debugPrint('🔥 Agora Chat: Login successful for userId: $username');
      return true;
    } on ChatError catch (e) {
      debugPrint(
        'Agora Chat: Login failed - Code: ${e.code}, Description: ${e.description}',
      );

      // Handle specific error codes
      if (e.code == 100) {
        debugPrint(
          'Error 100: App Key is invalid. Please double-check your App Key in the Agora Console.',
        );
      } else if (e.code == 200) {
        // User is already logged in - treat this as success
        debugPrint('Agora Chat: User already logged in, treating as success');
        _currentUserId = username;
        _currentToken = token;
        _isLoggedIn = true;
        return true;
      } else if (e.code == 202) {
        debugPrint('Error 202: User authentication failed.');
      } else if (e.code == 204) {
        debugPrint(
          'Error 204: User does not exist - may need to register first.',
        );
      }

      _isLoggedIn = false;
      return false;
    } catch (e) {
      debugPrint('Agora Chat: Login failed - Unexpected error: $e');
      _isLoggedIn = false;
      return false;
    }
  }

  Future<void> logout() async {
    if (!_isLoggedIn) return;

    try {
      await _chatClient!.logout();
      _isLoggedIn = false;
      _currentUserId = null;
      _currentToken = null;
      debugPrint('Agora Chat: Logged out successfully');
    } catch (e) {
      debugPrint('Agora Chat: Logout failed - $e');
    }
  }

  /// Send a text message to a user
  Future<bool> sendTextMessage(String toUserId, String content) async {
    debugPrint('🔥 Agora Chat: Attempting to send message to $toUserId');
    debugPrint('🔥 Agora Chat: Message content: $content');
    debugPrint('🔥 Agora Chat: Current status - $statusInfo');

    if (!_isLoggedIn) {
      debugPrint('🔥 Agora Chat: ERROR - Not logged in, cannot send message');
      debugPrint('🔥 Agora Chat: Current user ID: $_currentUserId');
      debugPrint('🔥 Agora Chat: Is initialized: $_isInitialized');
      return false;
    }

    if (_chatClient == null) {
      debugPrint('🔥 Agora Chat: ERROR - Chat client is null');
      return false;
    }

    try {
      debugPrint('🔥 Agora Chat: Creating message for target: $toUserId');
      final message = ChatMessage.createTxtSendMessage(
        targetId: toUserId,
        content: content,
      );

      debugPrint('🔥 Agora Chat: Message created with ID: ${message.msgId}');
      debugPrint('🔥 Agora Chat: Sending message via chat manager...');

      // Check if target user needs to be added as contact first
      try {
        final contacts = await _chatClient!.contactManager.fetchAllContactIds();
        final isContact = contacts.contains(toUserId);
        debugPrint('🔥 Agora Chat: Target user is in contacts: $isContact');

        if (!isContact) {
          debugPrint(
            '🔥 Agora Chat: ⚠️  Target user is not in contacts - this might prevent message delivery',
          );
          debugPrint('🔥 Agora Chat: Consider adding user to contacts first');
        }
      } catch (e) {
        debugPrint('🔥 Agora Chat: Could not check contact status: $e');
      }

      await _chatClient!.chatManager.sendMessage(message);

      debugPrint('🔥 Agora Chat: ✅ Message sent to queue for $toUserId');
      debugPrint('🔥 Agora Chat: Message ID: ${message.msgId}');
      debugPrint('🔥 Agora Chat: Message status: ${message.status}');
      debugPrint('🔥 Agora Chat: Message direction: ${message.direction}');
      debugPrint('🔥 Agora Chat: Message timestamp: ${message.serverTime}');
      debugPrint(
        '🔥 Agora Chat: Message conversation type: ${message.chatType}',
      );

      // Wait a moment and check if message appears in conversation
      await Future.delayed(const Duration(milliseconds: 500));

      try {
        final conversation =
            await _chatClient!.chatManager.getConversation(toUserId);
        if (conversation != null) {
          final messages = await conversation.loadMessages(loadCount: 1);
          debugPrint(
            '🔥 Agora Chat: Conversation now has ${messages.length} messages',
          );
          if (messages.isNotEmpty) {
            final lastMessage = messages.first;
            debugPrint('🔥 Agora Chat: Last message ID: ${lastMessage.msgId}');
            debugPrint(
              '🔥 Agora Chat: Last message status: ${lastMessage.status}',
            );
          }
        }
      } catch (e) {
        debugPrint(
          '🔥 Agora Chat: Could not verify message in conversation: $e',
        );
      }

      return true;
    } catch (e) {
      debugPrint('🔥 Agora Chat: ❌ Failed to send message - $e');
      debugPrint('🔥 Agora Chat: Error type: ${e.runtimeType}');

      if (e is ChatError) {
        debugPrint('🔥 Agora Chat: Error code: ${e.code}');
        debugPrint('🔥 Agora Chat: Error description: ${e.description}');
      }

      return false;
    }
  }

  Future<List<ChatConversation>> getConversations() async {
    if (!_isLoggedIn) {
      debugPrint('Agora Chat: Not logged in, cannot get conversations');
      return [];
    }

    try {
      final conversations =
          await _chatClient!.chatManager.loadAllConversations();
      debugPrint('Agora Chat: Loaded ${conversations.length} conversations');
      return conversations;
    } catch (e) {
      debugPrint('Agora Chat: Failed to load conversations - $e');
      return [];
    }
  }

  /// Get messages from a conversation with proper pagination
  Future<List<ChatMessage>> getMessages(
    String conversationId, {
    int count = 20,
    String? startMsgId,
    ChatSearchDirection direction = ChatSearchDirection.Up,
  }) async {
    if (!_isLoggedIn) {
      debugPrint('Agora Chat: Not logged in, cannot get messages');
      return [];
    }

    try {
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation == null) {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return [];
      }

      List<ChatMessage> messages;

      if (startMsgId != null) {
        // Load messages with pagination - use conversation's loadMessages with startMsgId
        messages = await conversation.loadMessages(
          startMsgId: startMsgId,
          loadCount: count,
        );
      } else {
        // Load latest messages from conversation
        messages = await conversation.loadMessages(
          loadCount: count,
        );
      }

      // Messages from Agora are already sorted by timestamp (newest first)
      debugPrint(
        'Agora Chat: Loaded ${messages.length} messages from $conversationId',
      );
      return messages;
    } catch (e) {
      debugPrint('Agora Chat: Failed to load messages - $e');
      return [];
    }
  }

  /// Check if user is logged in
  bool get isLoggedIn => _isLoggedIn;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get current user ID
  String? get currentUserId => _currentUserId;

  /// Validate that the current user matches expected username
  bool validateCurrentUser(String expectedUsername) {
    if (!_isLoggedIn || _currentUserId == null) {
      debugPrint('🔥 Agora Chat: User validation failed - not logged in');
      return false;
    }

    final isValid =
        _currentUserId!.toLowerCase() == expectedUsername.toLowerCase();
    if (!isValid) {
      debugPrint(
        '🔥 Agora Chat: User validation failed - '
        'expected: $expectedUsername, current: $_currentUserId',
      );
    } else {
      debugPrint(
        '🔥 Agora Chat: User validation successful - current: $_currentUserId',
      );
    }

    return isValid;
  }

  /// Get chat client instance
  ChatClient? get chatClient => _chatClient;

  /// Download attachment for a message
  Future<bool> downloadAttachment(ChatMessage message) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot download attachment - not logged in');
      return false;
    }

    try {
      await _chatClient!.chatManager.downloadAttachment(message);
      debugPrint('Agora Chat: Attachment downloaded successfully');
      return true;
    } catch (e) {
      debugPrint('Agora Chat: Failed to download attachment - $e');
      return false;
    }
  }

  /// Download thumbnail for a message
  Future<bool> downloadThumbnail(ChatMessage message) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot download thumbnail - not logged in');
      return false;
    }

    try {
      await _chatClient!.chatManager.downloadThumbnail(message);
      debugPrint('Agora Chat: Thumbnail downloaded successfully');
      return true;
    } catch (e) {
      debugPrint('Agora Chat: Failed to download thumbnail - $e');
      return false;
    }
  }

  /// Get detailed status for debugging
  String get statusInfo =>
      'Initialized: $_isInitialized, Logged in: $_isLoggedIn, User: $_currentUserId';

  /// Comprehensive diagnostic method for message delivery issues
  Future<Map<String, dynamic>> diagnoseMessageDeliveryIssues(
    String targetUserId,
  ) async {
    final diagnostics = <String, dynamic>{};

    debugPrint('🔥 Agora Chat: Starting message delivery diagnostics...');

    // 1. Check initialization
    diagnostics['isInitialized'] = _isInitialized;
    diagnostics['isLoggedIn'] = _isLoggedIn;
    diagnostics['currentUserId'] = _currentUserId;
    diagnostics['currentToken'] = _currentToken != null ? 'Present' : 'Missing';
    diagnostics['chatClientExists'] = _chatClient != null;

    // 2. Check connection status
    if (_chatClient != null) {
      try {
        final isConnected = await _chatClient!.isConnected();
        diagnostics['isConnected'] = isConnected;
        debugPrint('🔥 Agora Chat: Connection status: $isConnected');
      } catch (e) {
        diagnostics['connectionError'] = e.toString();
        debugPrint('🔥 Agora Chat: Error checking connection: $e');
      }
    }

    // 3. Check if target user exists (try to fetch their info)
    try {
      debugPrint(
        '🔥 Agora Chat: Checking if target user exists: $targetUserId',
      );
      final userInfo = await fetchUserAttributes(targetUserId);
      diagnostics['targetUserExists'] = true;
      diagnostics['targetUserInfo'] = userInfo;
      debugPrint('🔥 Agora Chat: Target user found: ${userInfo['nickname']}');
    } catch (e) {
      diagnostics['targetUserExists'] = false;
      diagnostics['targetUserError'] = e.toString();
      debugPrint('🔥 Agora Chat: Target user check failed: $e');
    }

    // 4. Check if target user is in contacts
    if (_chatClient != null) {
      try {
        final contacts = await _chatClient!.contactManager.fetchAllContactIds();
        final isContact = contacts.contains(targetUserId);
        diagnostics['isTargetInContacts'] = isContact;
        diagnostics['totalContacts'] = contacts.length;
        debugPrint('🔥 Agora Chat: Target is in contacts: $isContact');
        debugPrint('🔥 Agora Chat: Total contacts: ${contacts.length}');
      } catch (e) {
        diagnostics['contactsError'] = e.toString();
        debugPrint('🔥 Agora Chat: Error checking contacts: $e');
      }
    }

    // 5. Test message creation (without sending)
    try {
      final testMessage = ChatMessage.createTxtSendMessage(
        targetId: targetUserId,
        content: 'Test message creation',
      );
      diagnostics['canCreateMessage'] = true;
      diagnostics['testMessageId'] = testMessage.msgId;
      debugPrint('🔥 Agora Chat: Message creation test passed');
    } catch (e) {
      diagnostics['canCreateMessage'] = false;
      diagnostics['messageCreationError'] = e.toString();
      debugPrint('🔥 Agora Chat: Message creation test failed: $e');
    }

    // 6. Check conversation existence
    if (_chatClient != null) {
      try {
        final conversation =
            await _chatClient!.chatManager.getConversation(targetUserId);
        diagnostics['conversationExists'] = conversation != null;
        if (conversation != null) {
          // Try to get unread count instead of message count
          try {
            final unreadCount = await conversation.unreadCount();
            diagnostics['conversationUnreadCount'] = unreadCount;
            debugPrint(
              '🔥 Agora Chat: Conversation exists with $unreadCount unread messages',
            );
          } catch (e) {
            debugPrint('🔥 Agora Chat: Could not get unread count: $e');
            diagnostics['conversationUnreadCount'] = 'unknown';
          }
        }
      } catch (e) {
        diagnostics['conversationError'] = e.toString();
        debugPrint('🔥 Agora Chat: Error checking conversation: $e');
      }
    }

    debugPrint('🔥 Agora Chat: Diagnostics complete: $diagnostics');
    return diagnostics;
  }

  /// Test message delivery with comprehensive logging
  Future<bool> testMessageDelivery(
    String targetUserId,
    String testContent,
  ) async {
    debugPrint('🔥 Agora Chat: Starting message delivery test...');

    // First run diagnostics
    final diagnostics = await diagnoseMessageDeliveryIssues(targetUserId);

    // Check if basic requirements are met
    if (diagnostics['isInitialized'] != true ||
        diagnostics['isLoggedIn'] != true) {
      debugPrint(
        '🔥 Agora Chat: ❌ Test failed - Not properly initialized or logged in',
      );
      return false;
    }

    if (diagnostics['chatClientExists'] != true) {
      debugPrint('🔥 Agora Chat: ❌ Test failed - Chat client does not exist');
      return false;
    }

    // Attempt to send test message
    debugPrint('🔥 Agora Chat: Attempting to send test message...');
    final success = await sendTextMessage(targetUserId, testContent);

    if (success) {
      debugPrint('🔥 Agora Chat: ✅ Test message sent successfully!');
    } else {
      debugPrint('🔥 Agora Chat: ❌ Test message failed to send');
    }

    return success;
  }

  /// Add user as contact (might be required for message delivery)
  Future<bool> addContact(String userId, {String? reason}) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('🔥 Agora Chat: Cannot add contact - not logged in');
      return false;
    }

    try {
      debugPrint('🔥 Agora Chat: Adding $userId as contact...');
      await _chatClient!.contactManager.addContact(userId, reason: reason);
      debugPrint('🔥 Agora Chat: ✅ Successfully added $userId as contact');
      return true;
    } catch (e) {
      debugPrint('🔥 Agora Chat: ❌ Failed to add contact: $e');
      if (e is ChatError) {
        debugPrint('🔥 Agora Chat: Error code: ${e.code}');
        debugPrint('🔥 Agora Chat: Error description: ${e.description}');

        // Handle specific error codes
        if (e.code == 1301) {
          debugPrint(
            '🔥 Agora Chat: Contact request sent (waiting for approval)',
          );
          return true; // Consider this a success - request was sent
        }
      }
      return false;
    }
  }

  /// Check if user is already a contact
  Future<bool> isContact(String userId) async {
    if (_chatClient == null || !_isLoggedIn) {
      return false;
    }

    try {
      final contacts = await _chatClient!.contactManager.fetchAllContactIds();
      return contacts.contains(userId);
    } catch (e) {
      debugPrint('🔥 Agora Chat: Error checking contact status: $e');
      return false;
    }
  }

  /// Send contact request with custom message
  Future<bool> sendContactRequest(String userId, String message) async {
    return addContact(userId, reason: message);
  }

  /// Quick test method to try sending a message with all checks
  Future<Map<String, dynamic>> quickMessageTest(String targetUserId) async {
    final result = <String, dynamic>{};

    debugPrint('🔥 Agora Chat: Starting quick message test for $targetUserId');

    // Step 1: Run diagnostics
    final diagnostics = await diagnoseMessageDeliveryIssues(targetUserId);
    result['diagnostics'] = diagnostics;

    // Step 2: Check if user is in contacts, if not try to add them
    if (diagnostics['isTargetInContacts'] != true) {
      debugPrint('🔥 Agora Chat: Target not in contacts, attempting to add...');
      final addedAsContact =
          await addContact(targetUserId, reason: 'Test message');
      result['addedAsContact'] = addedAsContact;

      if (addedAsContact) {
        // Wait a moment for contact addition to sync
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    // Step 3: Try to send test message
    final testMessage = 'Test message from ${DateTime.now().toIso8601String()}';
    debugPrint('🔥 Agora Chat: Sending test message: $testMessage');

    final messageSent = await sendTextMessage(targetUserId, testMessage);
    result['messageSent'] = messageSent;
    result['testMessage'] = testMessage;
    result['timestamp'] = DateTime.now().toIso8601String();

    debugPrint('🔥 Agora Chat: Quick test complete: $result');
    return result;
  }

  /// Send read receipt for a message
  Future<bool> sendReadReceipt(String messageId, String fromUserId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot send read receipt - not logged in');
      return false;
    }

    try {
      // Find the message by ID to send read receipt
      final conversations =
          await _chatClient!.chatManager.loadAllConversations();
      ChatMessage? targetMessage;

      for (final conversation in conversations) {
        final messages = await conversation.loadMessages(
          loadCount: 50,
        );
        targetMessage = messages.firstWhere(
          (msg) => msg.msgId == messageId,
          orElse: () => throw StateError('Message not found'),
        );
        if (targetMessage.msgId == messageId) break;
      }

      if (targetMessage != null) {
        await _chatClient!.chatManager.sendMessageReadAck(targetMessage);
        debugPrint('Agora Chat: Read receipt sent for message: $messageId');
        return true;
      } else {
        debugPrint(
          'Agora Chat: Message not found for read receipt: $messageId',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Agora Chat: Failed to send read receipt - $e');
      return false;
    }
  }

  /// Send delivery receipt for a message
  Future<bool> sendDeliveryReceipt(String messageId, String fromUserId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot send delivery receipt - not logged in');
      return false;
    }

    try {
      // Agora Chat SDK handles delivery receipts automatically
      // This method is kept for consistency but may not be needed
      debugPrint(
        'Agora Chat: Delivery receipt handled automatically for message: $messageId',
      );
      return true;
    } catch (e) {
      debugPrint('Agora Chat: Failed to send delivery receipt - $e');
      return false;
    }
  }

  /// Mark conversation as read
  Future<bool> markConversationAsRead(String conversationId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint(
        'Agora Chat: Cannot mark conversation as read - not logged in',
      );
      return false;
    }

    try {
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation != null) {
        await conversation.markAllMessagesAsRead();
        debugPrint('Agora Chat: Conversation marked as read: $conversationId');
        return true;
      } else {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }
    } catch (e) {
      debugPrint('Agora Chat: Failed to mark conversation as read - $e');
      return false;
    }
  }

  // ignore: lines_longer_than_80_chars
  // Conversation Level Implementation to delete a conversation (Not sure if it's from both ends)
  Future<bool> deleteConversation(
    String conversationId, {
    bool deleteMessages = true,
  }) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot delete conversation - not logged in');
      return false;
    }

    try {
      await _chatClient!.chatManager.deleteConversation(
        conversationId,
        deleteMessages: deleteMessages,
      );
      debugPrint(
        'Agora Chat: Conversation deleted: $conversationId (deleteMessages: $deleteMessages)',
      );
      return true;
    } catch (e) {
      debugPrint('Agora Chat: Failed to delete conversation - $e');
      return false;
    }
  }

  /// Mute a conversation using Agora Chat SDK push notification settings
  Future<bool> muteConversation(
    String conversationId,
    Duration? duration,
  ) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot mute conversation - not logged in');
      return false;
    }

    try {
      // Check if conversation exists and get its type
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation == null) {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }

      // Use Agora Chat SDK to set conversation to silent mode
      // This is a simplified implementation using available SDK methods
      await _chatClient!.pushManager.setSilentModeForAll(
        param: ChatSilentModeParam.remindType(ChatPushRemindType.NONE),
      );

      debugPrint(
        'Agora Chat: Conversation notifications muted using SDK: $conversationId for ${duration?.toString() ?? 'indefinitely'}',
      );

      return true;
    } catch (e) {
      debugPrint('Agora Chat: Failed to mute conversation using SDK - $e');
      return false;
    }
  }

  /// Unmute a conversation using Agora Chat SDK push notification settings
  Future<bool> unmuteConversation(String conversationId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot unmute conversation - not logged in');
      return false;
    }

    try {
      // Check if conversation exists and get its type
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation == null) {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }

      // Use Agora Chat SDK to restore notifications for conversation
      // This is a simplified implementation using available SDK methods
      await _chatClient!.pushManager.setSilentModeForAll(
        param: ChatSilentModeParam.remindType(ChatPushRemindType.ALL),
      );

      debugPrint(
        'Agora Chat: Conversation notifications unmuted using SDK: $conversationId',
      );

      return true;
    } catch (e) {
      debugPrint('Agora Chat: Failed to unmute conversation using SDK - $e');
      return false;
    }
  }

  /// Archive a conversation - handled by HydratedBloc
  /// This method just validates the conversation exists
  Future<bool> archiveConversation(String conversationId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot archive conversation - not logged in');
      return false;
    }

    try {
      // Verify conversation exists
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation != null) {
        debugPrint(
          'Agora Chat: Conversation validated for archiving: $conversationId',
        );
        return true;
      } else {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }
    } catch (e) {
      debugPrint(
        'Agora Chat: Failed to validate conversation for archiving - $e',
      );
      return false;
    }
  }

  /// Unarchive a conversation - handled by HydratedBloc
  /// This method just validates the conversation exists
  Future<bool> unarchiveConversation(String conversationId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot unarchive conversation - not logged in');
      return false;
    }

    try {
      // Verify conversation exists
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation != null) {
        debugPrint(
          'Agora Chat: Conversation validated for unarchiving: $conversationId',
        );
        return true;
      } else {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }
    } catch (e) {
      debugPrint(
        'Agora Chat: Failed to validate conversation for unarchiving - $e',
      );
      return false;
    }
  }

  /// Pin a conversation - handled by HydratedBloc
  /// This method just validates the conversation exists
  Future<bool> pinConversation(String conversationId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot pin conversation - not logged in');
      return false;
    }

    try {
      // Verify conversation exists
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation != null) {
        debugPrint(
          'Agora Chat: Conversation validated for pinning: $conversationId',
        );
        return true;
      } else {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }
    } catch (e) {
      debugPrint(
        'Agora Chat: Failed to validate conversation for pinning - $e',
      );
      return false;
    }
  }

  /// Unpin a conversation - handled by HydratedBloc
  /// This method just validates the conversation exists
  Future<bool> unpinConversation(String conversationId) async {
    if (_chatClient == null || !_isLoggedIn) {
      debugPrint('Agora Chat: Cannot unpin conversation - not logged in');
      return false;
    }

    try {
      // Verify conversation exists
      final conversation =
          await _chatClient!.chatManager.getConversation(conversationId);
      if (conversation != null) {
        debugPrint(
          'Agora Chat: Conversation validated for unpinning: $conversationId',
        );
        return true;
      } else {
        debugPrint('Agora Chat: Conversation not found: $conversationId');
        return false;
      }
    } catch (e) {
      debugPrint(
        'Agora Chat: Failed to validate conversation for unpinning - $e',
      );
      return false;
    }
  }

  /// Get all contacts from server
  Future<List<String>> getContacts() async {
    if (_chatClient == null || !_isLoggedIn) {
      throw Exception('Chat client not initialized or not logged in');
    }

    try {
      final contacts = await _chatClient!.contactManager.fetchAllContactIds();
      debugPrint('Retrieved ${contacts.length} contacts from server');
      return contacts;
    } catch (e) {
      debugPrint('Error getting contacts: $e');
      rethrow;
    }
  }

  /// Get all blocked users from server
  Future<List<String>> getBlockedUsers() async {
    if (_chatClient == null || !_isLoggedIn) {
      throw Exception('Chat client not initialized or not logged in');
    }

    try {
      final blockedUsers = await _chatClient!.contactManager.fetchBlockIds();
      debugPrint('Retrieved ${blockedUsers.length} blocked users from server');
      return blockedUsers;
    } catch (e) {
      debugPrint('Error getting blocked users: $e');
      return [];
    }
  }

  /// Block a user
  Future<void> blockUser(String username) async {
    if (_chatClient == null || !_isLoggedIn) {
      throw Exception('Chat client not initialized or not logged in');
    }

    try {
      await _chatClient!.contactManager.addUserToBlockList(username);
      debugPrint('User blocked successfully: $username');
    } catch (e) {
      debugPrint('Error blocking user $username: $e');
      rethrow;
    }
  }

  /// Unblock a user
  Future<void> unblockUser(String username) async {
    if (_chatClient == null || !_isLoggedIn) {
      throw Exception('Chat client not initialized or not logged in');
    }

    try {
      await _chatClient!.contactManager.removeUserFromBlockList(username);
      debugPrint('User unblocked successfully: $username');
    } catch (e) {
      debugPrint('Error unblocking user $username: $e');
      rethrow;
    }
  }

  /// Handle contact added event
  void _onContactAdded(String userId) {
    debugPrint('Contact successfully added: $userId');
    // Broadcast contact added event to UI
    _contactAddedController.add(userId);
  }

  /// Fetch user attributes from Agora Chat REST API
  Future<Map<String, dynamic>> fetchUserAttributes(String userId) async {
    try {
      debugPrint(
        '🔍 AgoraChatService - Delegating to ChatRemoteDataSource for user attributes',
      );

      // Use the ChatRemoteDataSource implementation
      final chatRemoteDataSource = sl<ChatRemoteDataSource>();
      return await chatRemoteDataSource.fetchUserAttributes(userId);
    } catch (e) {
      debugPrint(
        '🔍 AgoraChatService - Error fetching user attributes for $userId: $e',
      );
      // Return default attributes if fetch fails
      return {
        'firstName': null,
        'lastName': null,
        'phoneNumber': null,
        'email': null,
        'nickname': userId,
      };
    }
  }

  /// Update user attributes in Agora Chat SDK
  Future<bool> updateUserAttributes({
    String? nickname,
    String? avatarUrl,
    String? email,
    String? phone,
    int? gender,
    String? signature,
    String? birth,
    String? ext,
  }) async {
    if (_chatClient == null || !_isLoggedIn) {
      throw Exception('Chat client not initialized or not logged in');
    }

    try {
      debugPrint('🔍 AgoraChatService: Updating user attributes...');

      final attributes = <String, String?>{};
      if (nickname != null) attributes['nickName'] = nickname;
      if (avatarUrl != null) attributes['avatarUrl'] = avatarUrl;
      if (email != null) attributes['mail'] = email;
      if (phone != null) attributes['phone'] = phone;
      if (signature != null) attributes['sign'] = signature;
      if (birth != null) attributes['birth'] = birth;
      if (ext != null) attributes['ext'] = ext;
      if (gender != null) attributes['gender'] = gender.toString();

      await _chatClient!.userInfoManager.updateUserInfo(
        nickname: nickname,
        avatarUrl: avatarUrl,
        mail: email,
        phone: phone,
        gender: gender,
        sign: signature,
        birth: birth,
        ext: ext,
      );

      debugPrint('🔍 AgoraChatService: Successfully updated user attributes');
      return true;
    } catch (e) {
      debugPrint('🔍 AgoraChatService: Error updating user attributes: $e');
      return false;
    }
  }

  /// Sync user profile data to Agora attributes
  Future<bool> syncUserProfileToAgora({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? avatarUrl,
    String? city,
    String? country,
    String? dateOfBirth,
    String? gender,
    String? memberCode,
  }) async {
    try {
      debugPrint('🔍 AgoraChatService: Syncing user profile to Agora...');

      // Create display name from first and last name
      final displayName = '$firstName $lastName'.trim();

      // Convert gender string to int
      var genderInt = 0;
      if (gender?.toLowerCase() == 'male') {
        genderInt = 1;
      } else if (gender?.toLowerCase() == 'female') {
        genderInt = 2;
      }

      // Create extended info JSON with additional metadata
      final extData = {
        'city': city,
        'country': country,
        'firstName': firstName,
        'lastName': lastName,
        'memberCode': memberCode,
      };
      final extString = extData.entries
          .where((e) => e.value?.isNotEmpty == true)
          .map((e) => '${e.key}:${e.value}')
          .join('|');

      final success = await updateUserAttributes(
        nickname: displayName,
        avatarUrl: avatarUrl,
        email: email,
        phone: phone,
        gender: genderInt,
        birth: dateOfBirth,
        ext: extString.isNotEmpty ? extString : null,
      );

      if (success) {
        debugPrint(
          '🔍 AgoraChatService: Successfully synced user profile to Agora',
        );
      } else {
        debugPrint('🔍 AgoraChatService: Failed to sync user profile to Agora');
      }

      return success;
    } catch (e) {
      debugPrint(
        '🔍 AgoraChatService: Error syncing user profile to Agora: $e',
      );
      return false;
    }
  }

  /// Get user display name by username (convenience method)
  Future<String> getUserDisplayName(String username) async {
    try {
      final attributes = await fetchUserAttributes(username);
      return attributes['nickname'] as String? ?? username;
    } catch (e) {
      debugPrint('Error getting display name for $username: $e');
      return username;
    }
  }

  /// Get user avatar URL by username (convenience method)
  Future<String?> getUserAvatarUrl(String username) async {
    try {
      final attributes = await fetchUserAttributes(username);
      final avatarUrl = attributes['avatarUrl'] as String?;
      return avatarUrl?.isNotEmpty == true ? avatarUrl : null;
    } catch (e) {
      debugPrint('Error getting avatar URL for $username: $e');
      return null;
    }
  }

  /// Get user contact info by username (convenience method)
  Future<Map<String, String?>> getUserContactInfo(String username) async {
    try {
      final attributes = await fetchUserAttributes(username);
      return {
        'email': attributes['email'] as String?,
        'phone': attributes['phone'] as String?,
      };
    } catch (e) {
      debugPrint('Error getting contact info for $username: $e');
      return {'email': null, 'phone': null};
    }
  }

  /// Get complete user profile by username (convenience method)
  Future<AgoraUser> getUserProfile(String username) async {
    try {
      final attributes = await fetchUserAttributes(username);
      return AgoraUser(
        username: username,
        displayName: attributes['nickname'] as String? ?? username,
        avatarUrl: attributes['avatarUrl'] as String?,
        email: attributes['email'] as String?,
        phone: attributes['phone'] as String?,
        signature: attributes['signature'] as String?,
        birth: attributes['birth'] as String?,
        gender: attributes['gender'] as int? ?? 0,
        ext: attributes['ext'] as String?,
      );
    } catch (e) {
      debugPrint('Error getting user profile for $username: $e');
      return AgoraUser(
        username: username,
        displayName: username,
      );
    }
  }

  /// Handle contact deleted event
  void _onContactDeleted(String userId) {
    debugPrint('Contact removed: $userId');
    // Broadcast contact deleted event to UI
    _contactDeletedController.add(userId);
  }

  /// Handle incoming message for real-time updates
  void _onMessageReceived(ChatMessage message) {
    debugPrint('Broadcasting new message: ${message.msgId}');
    _messageReceivedController.add(message);
  }

  /// Handle message status changes
  void _onMessageStatusChanged(ChatMessage message, String status) {
    debugPrint('Message ${message.msgId} status changed to: $status');
    _messageStatusController.add(message);
  }

  /// Handle message recall events
  void _onMessageRecalled(ChatMessage message) {
    debugPrint('Message recalled: ${message.msgId}');
    // Broadcast message recall event to UI
    _messageRecalledController.add(message);
  }

  /// Handle message content change events
  void _onMessageContentChanged(
    ChatMessage message,
    String operatorId,
    int operationTime,
  ) {
    debugPrint('Message content changed: ${message.msgId} by $operatorId');
    // Broadcast message content change event to UI
    _messageContentChangedController.add(message);
  }

  /// Handle presence status changes from Agora Chat SDK
  void _handlePresenceStatusChanged(List<ChatPresence> presenceList) {
    try {
      // Forward presence updates to PresenceService if it's registered
      if (sl.isRegistered<PresenceService>()) {
        final presenceService = sl<PresenceService>();
        presenceService.handlePresenceStatusChanged(presenceList);
      } else {
        debugPrint(
          'Agora Chat: PresenceService not registered, cannot handle presence updates',
        );
      }
    } catch (e) {
      debugPrint('Agora Chat: Error handling presence status change: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    _chatClient?.removeConnectionEventHandler('agora_chat_connection_handler');
    _chatClient?.chatManager.removeEventHandler('message_handler');
    _chatClient?.contactManager.removeEventHandler('agora_contact_handler');

    // Close stream controllers
    _contactAddedController.close();
    _contactDeletedController.close();
    _messageReceivedController.close();
    _messageStatusController.close();
    _messageRecalledController.close();
    _messageContentChangedController.close();
    _messageIdChangedController.close();

    _chatClient?.logout();
  }
}
