import 'dart:async';

import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/config/api_config.dart';
import 'package:cbrs/core/services/agora_chat_service.dart';
import 'package:cbrs/core/services/agora_contact_manager.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/connectivity/connectivity_service.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/device/device_service.dart';
import 'package:cbrs/core/services/device/session_timeout_service.dart';
import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/presence_service.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/core/services/voice_recording_service.dart';
import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/features/add_money/application/bloc/add_money_bloc.dart';
import 'package:cbrs/features/add_money/data/datasources/add_money_remote_datasource.dart';
import 'package:cbrs/features/add_money/data/datasources/add_money_remote_datasource_impl.dart';
import 'package:cbrs/features/add_money/data/repositories/add_money_repository_impl.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:cbrs/features/add_money/domain/usecases/add_money_usecase.dart';
import 'package:cbrs/features/add_money/domain/usecases/check_account_balance_usecase.dart';
import 'package:cbrs/features/add_money/domain/usecases/check_transfer_rules_usecase.dart';
import 'package:cbrs/features/add_money/domain/usecases/get_linked_accounts_usecase.dart'
    as add_money;
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
// Auth Injection
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/data/datasources/auth_remote_datasource.dart';
import 'package:cbrs/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:cbrs/features/auth/data/repositories/device_check_repository.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/create_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/forgot_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/forgot_pin_with_phone.dart';
import 'package:cbrs/features/auth/domain/usecases/login_with_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/logout.dart';
import 'package:cbrs/features/auth/domain/usecases/resend_email_verification.dart';
import 'package:cbrs/features/auth/domain/usecases/resend_otp.dart';
import 'package:cbrs/features/auth/domain/usecases/reset_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/sign_in_with_email.dart';
import 'package:cbrs/features/auth/domain/usecases/sign_in_with_phone.dart';
import 'package:cbrs/features/auth/domain/usecases/sign_up.dart';
import 'package:cbrs/features/auth/domain/usecases/unlink_device.dart';
import 'package:cbrs/features/auth/domain/usecases/verify_email.dart';
import 'package:cbrs/features/auth/domain/usecases/verify_otp.dart';
import 'package:cbrs/features/balance_check/application/bloc/balance_check_bloc.dart';
import 'package:cbrs/features/balance_check/application/bloc/recent_linked_transaction_bloc.dart';
import 'package:cbrs/features/balance_check/data/datasources/balance_check_remote_datasource.dart';
import 'package:cbrs/features/balance_check/data/repositories/balance_check_repository_impl.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_account_transactions_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_all_transactions.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_linked_accounts_usecase.dart'
    as balance;
import 'package:cbrs/features/balance_check/domain/usecases/get_recent_transactions_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_wallet_balance_usecase.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_bloc.dart';
import 'package:cbrs/features/cash_in_out/data/datasources/agent_remote_datasource.dart';
import 'package:cbrs/features/cash_in_out/data/datasources/agent_remote_datasource_impl.dart';
import 'package:cbrs/features/cash_in_out/data/datasources/bill_remote_datasource.dart';
import 'package:cbrs/features/cash_in_out/data/datasources/voucher_remote_datasource.dart';
import 'package:cbrs/features/cash_in_out/data/datasources/voucher_remote_datasource_impl.dart';
import 'package:cbrs/features/cash_in_out/data/repositories/agent_repository_impl.dart';
import 'package:cbrs/features/cash_in_out/data/repositories/bill_repository_impl.dart';
import 'package:cbrs/features/cash_in_out/data/repositories/otp_verification_repository_impl.dart';
import 'package:cbrs/features/cash_in_out/data/repositories/payment_confirmation_repository_impl.dart';
import 'package:cbrs/features/cash_in_out/data/repositories/transfer_rules_repository_impl.dart';
import 'package:cbrs/features/cash_in_out/data/repositories/voucher_repository_impl.dart';
import 'package:cbrs/features/cash_in_out/domain/repositories/agent_repository.dart';
import 'package:cbrs/features/cash_in_out/domain/repositories/bill_repository.dart';
import 'package:cbrs/features/cash_in_out/domain/repositories/otp_verification_repository.dart';
import 'package:cbrs/features/cash_in_out/domain/repositories/payment_confirmation_repository.dart';
import 'package:cbrs/features/cash_in_out/domain/repositories/transfer_rules_repository.dart';
import 'package:cbrs/features/cash_in_out/domain/repositories/voucher_repository.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/cash_in_cash_out_check_transfer_rules_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/cash_out_resend_otp_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/confirm_payment_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/generate_bill_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/generate_voucher_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/parse_agent_qr_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/search_agent_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/verify_otp_usecase.dart';
import 'package:cbrs/features/change_to_birr/application/bloc/change_to_birr_bloc.dart';
// Change to Birr

import 'package:cbrs/features/change_to_birr/data/datasources/change_to_birr_remote_datasource.dart';
import 'package:cbrs/features/change_to_birr/data/datasources/change_to_birr_remote_datasource_impl.dart';
import 'package:cbrs/features/change_to_birr/data/repositories/change_to_birr_repository_impl.dart';
import 'package:cbrs/features/change_to_birr/domain/repositories/change_to_birr_repository.dart';
import 'package:cbrs/features/change_to_birr/domain/usecases/change_to_birr_usecase.dart';
import 'package:cbrs/features/change_to_birr/domain/usecases/check_transfer_rules_usecase.dart';
// Chat imports
import 'package:cbrs/features/chat/data/datasources/chat_remote_datasource.dart';
import 'package:cbrs/features/chat/data/repositories/chat_repository_impl.dart';
import 'package:cbrs/features/chat/domain/repositories/chat_repository.dart';
import 'package:cbrs/features/chat/domain/usecases/archive_conversation.dart'
    as archive_conversation_usecase;
import 'package:cbrs/features/chat/domain/usecases/delete_conversation.dart'
    as delete_conversation_usecase;
import 'package:cbrs/features/chat/domain/usecases/download_file.dart';
import 'package:cbrs/features/chat/domain/usecases/get_conversations.dart';
import 'package:cbrs/features/chat/domain/usecases/get_messages.dart';
import 'package:cbrs/features/chat/domain/usecases/manage_contacts.dart';
import 'package:cbrs/features/chat/domain/usecases/message_actions.dart'
    as message_actions;
import 'package:cbrs/features/chat/domain/usecases/mute_conversation.dart'
    as mute_conversation_usecase;
import 'package:cbrs/features/chat/domain/usecases/pin_conversation.dart'
    as pin_conversation_usecase;
import 'package:cbrs/features/chat/domain/usecases/send_message.dart';
import 'package:cbrs/features/chat/domain/usecases/presence_actions.dart'
    as presence_actions;
import 'package:cbrs/features/chat/domain/usecases/member_lookup.dart';
import 'package:cbrs/features/chat/domain/services/conversation_service.dart';
import 'package:cbrs/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:cbrs/features/chat/presentation/cubit/chat_app_bar_cubit.dart';
import 'package:cbrs/features/chat/presentation/cubit/chat_room_management_cubit.dart';
import 'package:cbrs/features/chat/presentation/cubit/conversation_metadata_cubit.dart';
import 'package:cbrs/features/chat/presentation/cubit/conversation_selection_cubit.dart';
import 'package:cbrs/features/chat/presentation/cubit/presence_cubit.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_bloc.dart';
import 'package:cbrs/features/exchange_rate/data/datasource/exchange_rate_remote_datasource.dart';
import 'package:cbrs/features/exchange_rate/data/repositories/exchange_rate_imp.dart';
import 'package:cbrs/features/exchange_rate/domain/repositories/exchange_rate_repositories.dart';
import 'package:cbrs/features/exchange_rate/domain/usecases/get_exchange_rates_usecase.dart';
import 'package:cbrs/features/gift_packages/application/bloc/packages_bloc.dart';
import 'package:cbrs/features/gift_packages/data/datasources/gift_package_local_data_source.dart';
// Gift Packages

// Load Wallet
import 'package:cbrs/features/gift_packages/data/datasources/gift_package_remote_data_source.dart';
import 'package:cbrs/features/gift_packages/data/repositories/gift_package_repository_impl.dart';
import 'package:cbrs/features/gift_packages/domain/repositories/gift_package_repository.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/confirm_gift_package_payment_usecase.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_banner_categories.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_banner_packages.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_banners.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_categories.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_gift_packages.dart';
import 'package:cbrs/features/gift_packages/domain/usecases/get_package_detail.dart';
// Authentication

import 'package:cbrs/features/gift_packages/domain/usecases/member_lookup.dart';
import 'package:cbrs/features/guest/application/bloc/guest_bloc.dart';
import 'package:cbrs/features/guest/data/data_source/guest_user_remote_data_source.dart';
import 'package:cbrs/features/guest/data/repository/guest_repo_impl.dart';
import 'package:cbrs/features/guest/domain/repository/guest_user_repository.dart';
import 'package:cbrs/features/guest/domain/usecases/guest_account_lookup.dart';
import 'package:cbrs/features/guest/domain/usecases/guest_transfer_initiate.dart';
import 'package:cbrs/features/guest/domain/usecases/guest_transfer_status_check.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/identity_verification/data/datasources/complycube_remote_datasource.dart';
import 'package:cbrs/features/identity_verification/data/datasources/complycube_remote_datasource_impl.dart';
import 'package:cbrs/features/identity_verification/data/datasources/identity_verification_remote_datasource.dart';
import 'package:cbrs/features/identity_verification/data/datasources/identity_verification_remote_datasource_impl.dart';
import 'package:cbrs/features/identity_verification/data/repositories/identity_verification_repository_impl.dart';
import 'package:cbrs/features/identity_verification/domain/repositories/identity_verification_repository.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/check_complycube_status_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/upload_document_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/create_complycube_client_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/generate_complycube_sdk_token_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/process_complycube_verification_usecase.dart';
// Identity Verification
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_bloc.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/complycube_bloc.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/app_update_bloc.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/in_app_update_controller.dart';
import 'package:cbrs/features/in_app_update/data/data_source/in_app_remote_data_source.dart';
import 'package:cbrs/features/in_app_update/data/repository/in_app_update_repository_impl.dart';
import 'package:cbrs/features/in_app_update/domain/repository/in_app_update_repository.dart';
import 'package:cbrs/features/in_app_update/domain/usecases/check_update_use_case.dart';
import 'package:cbrs/features/link_acccount/application_layer/bloc/link_account_bloc.dart';
import 'package:cbrs/features/link_acccount/data/data_source/link_account_remote_data_source.dart';
import 'package:cbrs/features/link_acccount/data/repositories_impl/link_account_repositories_impl.dart';
import 'package:cbrs/features/link_acccount/domain/repositories/link_account_repositories.dart';
import 'package:cbrs/features/link_acccount/domain/usecases/generate_link_account_usecase.dart';
import 'package:cbrs/features/link_acccount/domain/usecases/pended_link_account_usecase.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_bloc.dart';
import 'package:cbrs/features/load_wallet/data/datasources/remote_data_source.dart';
import 'package:cbrs/features/load_wallet/data/repositories/load_wallet_repository_impl.dart';
import 'package:cbrs/features/load_wallet/domain/repositories/load_wallet_repository.dart';
import 'package:cbrs/features/load_wallet/domain/usecases/check_load_wallet_status_usecase.dart';
import 'package:cbrs/features/load_wallet/domain/usecases/load_to_wallet_usecase.dart';
// Loans
import 'package:cbrs/features/loans/data/datasources/loan_remote_data_source.dart';
import 'package:cbrs/features/loans/data/datasources/loan_remote_data_source_impl.dart';
import 'package:cbrs/features/loans/data/repositories/loan_repository_impl.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:cbrs/features/loans/domain/usecases/apply_for_loan.dart';
import 'package:cbrs/features/loans/domain/usecases/calculate_loan_payment.dart'
    as loans;
import 'package:cbrs/features/loans/domain/usecases/confirm_loan_payment.dart';
import 'package:cbrs/features/loans/domain/usecases/generate_application_transaction.dart'
    as loans_generate;
import 'package:cbrs/features/loans/domain/usecases/get_all_loan_categories_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/get_loan_banks.dart';
import 'package:cbrs/features/loans/domain/usecases/get_loan_categories_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/get_loan_category_by_id_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:cbrs/features/loans/domain/usecases/loan_confirm_resend_otp_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/loan_confirm_with_otp_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/loan_confirm_with_pin_usecase.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_banks_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_categories_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_payment_info_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_cubit.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_bloc.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_payment_bloc.dart';
import 'package:cbrs/features/merchant_payment/data/datasources/merchant_payment_remote_data_source.dart';
import 'package:cbrs/features/merchant_payment/data/repositories/merchant_payment_repository_impl.dart';
import 'package:cbrs/features/merchant_payment/data/repositories/merchant_repository_impl.dart';
import 'package:cbrs/features/merchant_payment/domain/repositories/merchant_payment_repository.dart';
import 'package:cbrs/features/merchant_payment/domain/repositories/merchant_repository.dart';
import 'package:cbrs/features/merchant_payment/domain/usecases/check_merchant_payment_rules.dart';
import 'package:cbrs/features/merchant_payment/domain/usecases/get_merchant_by_till_number.dart';
import 'package:cbrs/features/merchant_payment/domain/usecases/merchant_payment.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/data/datasources/miniapp_remote_data_source.dart';
import 'package:cbrs/features/mini_apps/data/repositories/miniapp_repository_impl.dart';
import 'package:cbrs/features/mini_apps/domain/repositories/miniapp_repository.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/create_order_use_case.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/get_miniapp_use_case.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/submit_otp_use_case.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/submit_pin_use_case.dart';
import 'package:cbrs/features/mini_statements/applications/mini_statement_bloc.dart';
import 'package:cbrs/features/mini_statements/data/datasource/remote_data_source.dart';
import 'package:cbrs/features/mini_statements/data/repository_impl/repository_implementations.dart';
import 'package:cbrs/features/mini_statements/domain/repository/mini_statement_repository.dart';
import 'package:cbrs/features/mini_statements/domain/usecases/generate_mini_statement_usecase.dart';
import 'package:cbrs/features/money_request/data/data_sources/money_request_remote_datasource.dart';
import 'package:cbrs/features/money_request/data/data_sources/money_request_remote_datasource_impl.dart';
import 'package:cbrs/features/money_request/data/repositories/money_request_repository_impl.dart';
import 'package:cbrs/features/money_request/domain/repositories/money_request_repository.dart';
import 'package:cbrs/features/money_request/domain/usecases/accept_request_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/cancel_request_usercase.dart';
import 'package:cbrs/features/money_request/domain/usecases/confirm_request_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/get_request_list_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/get_wallet_detail_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/member_lookup_usecase.dart'
    as addMoney;
import 'package:cbrs/features/money_request/domain/usecases/reject_request_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/send_money_request_usecase.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_list/money_request_list_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_money_request_add_money/send_money_request_add_money_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_money_request_member_lookup/send_money_request_member_lookup_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_request_money_confirm_request/send_request_money_confirm_request_bloc.dart';
// My Connect
import 'package:cbrs/features/my_connect/applications/bloc/my_connect_bloc.dart';
import 'package:cbrs/features/my_connect/data/datasources/my_connect_remote_datasource.dart';
import 'package:cbrs/features/my_connect/data/repositories/my_connect_repository_impl.dart';
import 'package:cbrs/features/my_connect/domain/repositories/my_connect_repository.dart';
import 'package:cbrs/features/my_connect/domain/usecases/my_connect_accept_request_usecase.dart';
import 'package:cbrs/features/my_connect/domain/usecases/my_connect_fetch_connects_use_cases.dart';
import 'package:cbrs/features/my_connect/domain/usecases/my_connect_send_request_usecase.dart';
// My Loan
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_bloc.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/upfront_payment_bloc.dart';
import 'package:cbrs/features/my_loan/data/data_source/my_loan_remote_data_source.dart';
import 'package:cbrs/features/my_loan/data/repositories/repayment_repositiories_impl.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/my_loan/domain/usecases/confirm_monthly_repayment_with_pin.dart';
import 'package:cbrs/features/my_loan/domain/usecases/confirm_upfront_with_pin_usecase.dart';
import 'package:cbrs/features/my_loan/domain/usecases/generate_upfront_payment_usecase.dart';
import 'package:cbrs/features/my_loan/domain/usecases/my_loan_resend_otp.dart';
import 'package:cbrs/features/my_loan/domain/usecases/my_loan_verify_otp.dart';
import 'package:cbrs/features/my_loan/domain/usecases/pay_monthly_repayment_usecase.dart';
import 'package:cbrs/features/my_loan/domain/usecases/repaymnet_usecases.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_bloc.dart';
import 'package:cbrs/features/notifications/data/datasources/notification_remote_datasource.dart';
import 'package:cbrs/features/notifications/data/repositories/notification_repository_impl.dart';
import 'package:cbrs/features/notifications/domain/repositories/notification_repository.dart';
import 'package:cbrs/features/notifications/domain/usecases/fetch_notifications.dart';
import 'package:cbrs/features/notifications/domain/usecases/fetch_unseen_count.dart';
// Notifications
import 'package:cbrs/features/notifications/domain/usecases/mark_all_as_read_usecase.dart';
import 'package:cbrs/features/notifications/domain/usecases/mark_notification_as_read.dart';
// Onboarding
import 'package:cbrs/features/onboarding/data/datasource/onboarding_local_datasource.dart';
import 'package:cbrs/features/onboarding/data/repository/onboarding_repo_impl.dart';
import 'package:cbrs/features/onboarding/domain/repository/onboarding_repo.dart';
import 'package:cbrs/features/onboarding/domain/usecases/cache_first_timer.dart';
import 'package:cbrs/features/onboarding/domain/usecases/check_if_user_first_timer.dart';
import 'package:cbrs/features/onboarding/presentation/cubit/on_boarding_cubit.dart';
import 'package:cbrs/features/orders/application/blocs/order_bloc.dart';
import 'package:cbrs/features/orders/data/datasources/order_local_datasource.dart';
import 'package:cbrs/features/orders/data/datasources/order_remote_datasource.dart';
import 'package:cbrs/features/orders/data/repositories/order_repository_impl.dart';
import 'package:cbrs/features/orders/domain/repositories/order_repository.dart';
import 'package:cbrs/features/pay_by_qr/application/bloc/parse_qr_bloc.dart';
import 'package:cbrs/features/pay_by_qr/data/datasources/qr_local_datasource.dart';
import 'package:cbrs/features/pay_by_qr/data/datasources/qr_remote_datasource.dart';
import 'package:cbrs/features/pay_by_qr/data/repositories/qr_repository_impl.dart';
import 'package:cbrs/features/pay_by_qr/domain/repositories/qr_repository.dart';
import 'package:cbrs/features/pay_by_qr/domain/usecases/generate_qr_code_usecase.dart';
import 'package:cbrs/features/pay_by_qr/domain/usecases/parse_qr_code_usecase.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
// Profile
import 'package:cbrs/features/profile/data/datasources/profile_local_datasource.dart';
import 'package:cbrs/features/profile/data/datasources/profile_remote_datasource.dart';
import 'package:cbrs/features/profile/data/repositories/profile_repository_impl.dart';
import 'package:cbrs/features/profile/domain/repositories/profile_repositories.dart';
import 'package:cbrs/features/profile/domain/usecases/delete_avatar_use_case.dart';
import 'package:cbrs/features/profile/domain/usecases/update_avatar_usecase.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/send_money/data/datasources/bank_local_datasource.dart';
// Send Money
import 'package:cbrs/features/send_money/data/datasources/bank_transfer_remote_datasource.dart';
import 'package:cbrs/features/send_money/data/models/bank_model_adapter.dart';
import 'package:cbrs/features/send_money/data/models/bank_models.dart' as banks;
import 'package:cbrs/features/send_money/data/repositories/bank_transfer_repository_impl.dart';
import 'package:cbrs/features/send_money/domain/entities/recent_transaction_hive.dart';
import 'package:cbrs/features/send_money/domain/repositories/bank_transfer_repository.dart';
import 'package:cbrs/features/send_money/domain/usecases/check_transfer_rules.dart';
import 'package:cbrs/features/send_money/domain/usecases/check_transfer_status.dart';
import 'package:cbrs/features/send_money/domain/usecases/get_exchange_rate.dart';
import 'package:cbrs/features/send_money/domain/usecases/get_supported_banks.dart';
import 'package:cbrs/features/send_money/domain/usecases/get_supported_paginated_banks.dart';
import 'package:cbrs/features/send_money/domain/usecases/get_wallet_details.dart';
import 'package:cbrs/features/send_money/domain/usecases/lookup_account.dart';
import 'package:cbrs/features/send_money/domain/usecases/transfer_to_bank.dart';
import 'package:cbrs/features/send_money/domain/usecases/validate_transfer_amount.dart';
import 'package:cbrs/features/top_up/applications/top_up_bloc.dart';
import 'package:cbrs/features/top_up/data/data_sources/top_up_remote_data_source.dart';
import 'package:cbrs/features/top_up/data/repositories_impl/top_up_repositories_impl.dart';
import 'package:cbrs/features/top_up/domain/repositories/top_up_repositories.dart';
import 'package:cbrs/features/top_up/domain/usecases/top_up_create_usecase.dart';
import 'package:cbrs/features/top_up/domain/usecases/top_up_get_providers.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_details_bloc.dart';
import 'package:cbrs/features/transactions/data/datasources/transaction_local_data_source.dart';
import 'package:cbrs/features/transactions/data/datasources/transaction_remote_datasource.dart';
import 'package:cbrs/features/transactions/data/repositories/transaction_repository_impl.dart';
// Feature Injections
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:cbrs/features/transactions/domain/usecases/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_invoice.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_loan_invoice.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_recent_wallet_transfers.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_transaction_details.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_transactions.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_transfer_limit.dart';
import 'package:cbrs/features/transactions/domain/usecases/resend_otp.dart';
import 'package:cbrs/features/transactions/domain/usecases/validate_transaction_usecase.dart';
import 'package:cbrs/features/transactions/domain/usecases/verify_otp.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
// Transfer to Wallet
import 'package:cbrs/features/transfer_to_wallet/data/datasources/wallet_transfer_remote_datasource.dart';
import 'package:cbrs/features/transfer_to_wallet/data/repositories/wallet_transfer_repository_impl.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/repositories/wallet_transfer_repository.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/check_transfer_rules.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/check_transfer_status.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/generate_reciept_usecase.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/get_users_avatar_use_case.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/lookup_contacts.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/lookup_member.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/transfer_to_wallet.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/update_qr_status_usecase.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/validate_transfer_amount.dart';
import 'package:cbrs/features/utility/application/bloc/utility_bloc.dart';
import 'package:cbrs/features/utility/data/datasources/utility_remote_data_source.dart';
import 'package:cbrs/features/utility/data/repositories_impl/utility_repository_impl.dart';
import 'package:cbrs/features/utility/domain/repositories/utility_repository.dart';
import 'package:cbrs/features/utility/domain/usecases/create_order_use_case.dart';
import 'package:cbrs/features/utility/domain/usecases/get_utilities_use_case.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:local_auth/local_auth.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'injection_container.main.dart';
