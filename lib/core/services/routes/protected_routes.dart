part of 'route.dart';

final _protectedRoutes = [
  ...transferRoutes,
  ...loanRoutes,
  ...chatRoutes,
  GoRoute(
    name: AppRouteName.identityVerification,
    path: 'identity-verification',
    pageBuilder: (context, state) => _pageBuilder(
      ProtectedRoute(
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => sl<IdentityVerificationBloc>(),
            ),
            BlocProvider(
              create: (context) => sl<ComplyCubeBloc>(),
            ),
          ],
          child: const IdentityVerificationPage(),
        ),
      ),
      state,
    ),
    routes: [
      GoRoute(
        name: AppRouteName.complyCubeVerification,
        path: 'complycube-verification',
        pageBuilder: (context, state) => _pageBuilder(
          ProtectedRoute(
            child: BlocProvider(
              create: (context) => sl<ComplyCubeBloc>(),
              child: const ComplyCubeVerificationPage(),
            ),
          ),
          state,
        ),
      ),
      GoRoute(
        name: AppRouteName.faceScanCamera,
        path: 'face-scan',
        pageBuilder: (context, state) => _pageBuilder(
          BlocProvider(
            create: (context) => sl<IdentityVerificationBloc>(),
            child: const ProtectedRoute(
              child: FaceScanCameraPage(),
            ),
          ),
          state,
        ),
      ),
      GoRoute(
        name: AppRouteName.documentScan,
        path: 'document-scan',
        pageBuilder: (context, state) => _pageBuilder(
          BlocProvider(
            create: (context) => sl<IdentityVerificationBloc>(),
            child: ProtectedRoute(
              child: DocumentScanPage(
                selfieImage: state.extra as File?,
              ),
            ),
          ),
          state,
        ),
      ),
    ],
  ),
  GoRoute(
    name: AppRouteName.home,
    path: '/main',
    pageBuilder: (context, state) {
      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => sl<HomeBloc>(),
            ),
            BlocProvider(
              create: (context) => sl<TransactionBloc>()
                ..add(
                  const FetchTransactionsEvent(
                    page: 1,
                    perPage: 10,
                  ),
                ),
            ),
            BlocProvider(
              create: (context) => sl<RepaymentBloc>(),
            ),
            BlocProvider(
              create: (context) => sl<ProfileBloc>(),
            ),
          ],
          child: MainPage(extra: state.extra as Map<String, dynamic>?),
        ),
        state,
      );
    },
    routes: [
      // Notifications Route
      GoRoute(
        name: AppRouteName.notifications,
        path: AppRouteName.notifications,
        pageBuilder: (context, state) {
          final bloc = context.read<NotificationBloc>();

          return _pageBuilder(
            BlocProvider.value(
              value: bloc,

              //(context) => sl<NotificationBloc>(),
              child: const NotificationsScreen(),
            ),
            state,
          );
        },
        routes: [
          GoRoute(
            name: AppRouteName.notificationDetails,
            path: AppRouteName.notificationDetails,
            pageBuilder: (context, state) => _pageBuilder(
              NotificationDetailsScreen(
                notification: state.extra as NotificationItem,
              ),
              state,
            ),
          ),
        ],
      ),

      // Change to Birr Routes

      GoRoute(
        name: AppRouteName.guestRecipientAccount,
        path: 'guest-recipient-account',
        pageBuilder: (context, state) {
          final bank = (state.extra as Map<String, dynamic>)['bank'] as Bank;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<GuestBloc>(),
              child: Builder(
                builder: (context) =>
                    GuestRecipientAccountNumberScreen(bank: bank),
              ),
            ),
            state,
          );
        },
        routes: [
          GoRoute(
            name: AppRouteName.guestAddAmount,
            path: 'add-amount',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return CustomTransitionPage(
                key: state.pageKey,
                child: MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: context.read<ExchangeRateBloc>(),
                    ),
                    BlocProvider(
                      create: (context) => sl<GuestBloc>(),
                    ),
                  ],
                  child: GuestAddAmountScreen(
                    senderName: extra['senderName'] as String,
                    recipientName: extra['recipientName'] as String,
                    recipientAccount: extra['recipientAccountNumber'] as String,
                    bank: extra['bank'] as Bank,
                    reason: extra['reason'] as String?,
                  ),
                ),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  return FadeTransition(opacity: animation, child: child);
                },
              );
            },
          ),
          GoRoute(
            name: AppRouteName.guestConfirmation,
            path: 'confirmation',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<GuestBloc>(),
                  child: GuestConfirmationScreen(
                    amount: extra['amount'] as String,
                    recipientAccount: extra['recipientAccount'] as String,
                    recipientName: extra['recipientName'] as String,
                    bankCode: extra['bankCode'] as String,
                    reason: extra['reason'] as String?,
                    bankName: extra['bankName'] as String,
                    senderName: extra['senderName'] as String,
                    exchangeRate: extra['exchangeRate'] as double,
                  ),
                ),
                state,
              );
            },
          ),
        ],
      ),

      GoRoute(
        name: AppRouteName.commingsoon,
        path: AppRouteName.commingsoon,
        pageBuilder: (context, state) => _pageBuilder(
          const ComingSoonPage(),
          state,
        ),
      ),

      GoRoute(
        name: AppRouteName.profileInfo,
        path: AppRouteName.profileInfo,
        pageBuilder: (context, state) {
          return _pageBuilder(
            _wrapWithProfile(
              const ProfileInfoScreen(),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.profilePhoto,
        path: AppRouteName.profilePhoto,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            _wrapWithProfile(
              ProfilePhotoDetail(photoUrl: extra['photoUrl'] as String),
            ),
            state,
          );
        },
      ),
      GoRoute(
        name: AppRouteName.updatePin,
        path: AppRouteName.updatePin,
        pageBuilder: (context, state) {
          return _pageBuilder(
            _wrapWithProfile(
              const ChangePinScreen(),
            ),
            state,
          );
        },
      ),

      // add Email
      GoRoute(
        name: AppRouteName.addEmail,
        path: AppRouteName.addEmail,
        pageBuilder: (context, state) => _pageBuilder(
          _wrapWithProfile(
            AddEmailScreen(
              email: (state.extra as Map)['email'] as String,
            ),
          ),
          state,
        ),
        routes: [
          GoRoute(
            name: AppRouteName.verifyAddedEmail,
            path: AppRouteName.verifyAddedEmail,
            pageBuilder: (context, state) => _pageBuilder(
              _wrapWithProfile(
                VerifyEmailPhoneScreen(
                  email: (state.extra as Map)['email'] as String,
                ),
              ),
              state,
            ),
          ),
        ],
      ),

      // TODO
      // add Phone
      GoRoute(
        name: AppRouteName.addPhone,
        path: AppRouteName.addPhone,
        pageBuilder: (context, state) => _pageBuilder(
          _wrapWithProfile(
            AddPhoneScreen(
              phone: (state.extra as Map)['phoneNumber'] as String,
            ),
          ),
          state,
        ),
        routes: [
          GoRoute(
            name: AppRouteName.verifyAddedPhone,
            path: AppRouteName.verifyAddedPhone,
            pageBuilder: (context, state) {
              final extra = state.extra as Map?;
              return _pageBuilder(
                _wrapWithProfile(
                  VerifyEmailPhoneScreen(
                    phone: extra?['phone'] as String? ?? '',
                    isPhoneVerify: true,
                  ),
                ),
                state,
              );
            },
          ),
        ],
      ),

      // Customer Support
      GoRoute(
        name: AppRouteName.customerSupport,
        path: AppRouteName.customerSupport,
        pageBuilder: (context, state) => _pageBuilder(
          const CustomerSupportScreen(),
          state,
        ),
      ),

      // FAQ
      GoRoute(
        name: AppRouteName.faq,
        path: AppRouteName.faq,
        pageBuilder: (context, state) => _pageBuilder(
          const FAQScreen(),
          state,
        ),
      ),

      GoRoute(
        name: AppRouteName.privacyPolicy,
        path: AppRouteName.privacyPolicy,
        pageBuilder: (context, state) => _pageBuilder(
          const PrivacyAndPolicy(),
          state,
        ),
      ),

      // termsAndConditions
      GoRoute(
        name: AppRouteName.termsAndConditions,
        path: AppRouteName.termsAndConditions,
        pageBuilder: (context, state) => _pageBuilder(
          const TermsAndConditions(),
          state,
        ),
      ),

      GoRoute(
        name: AppRouteName.loanDetailPage,
        path: AppRouteName.loanDetailPage,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return _pageBuilder(
            MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => sl<RepaymentBloc>(),
                ),
                BlocProvider(
                  create: (context) => sl<UpfrontPaymentBloc>(),
                ),
              ],
              child: ScreenMyLoanDetailPage(
                // loanStatus: extra['loanStatus'] as String,
                loanType: extra['loanType'] as String,

                loanId: extra['loanId'] as String,
              ),
            ),
            state,
          );
        },
        routes: [
          GoRoute(
            name: AppRouteName.paymentDetail,
            path: AppRouteName.paymentDetail,
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                ScreenPaymentDetail(
                  paymentHistoryTimelineEntity:
                      extra['paymentHistoryTimelineEntity'] != null
                          ? extra['paymentHistoryTimelineEntity']
                              as PaymentHistoryTimelineEntity
                          : null,
                  applicationUpfrontFeeHistory:
                      extra['applicationUpfrontFeeHistory'] != null
                          ? extra['applicationUpfrontFeeHistory']
                              as ApplicationUpfrontFeeHistoryEntity
                          : null,
                  loanName: extra['loanName'] as String,
                  loanType: extra['loanType'] as String,
                ),
                state,
              );
            },
          ),
        ],
      ),

      GoRoute(
        name: AppRouteName.mortgageWebView,
        path: '/mortgage-webview',
        builder: (context, state) => VirtualTourWebView(
          url: state.uri.queryParameters['url'] ?? '',
        ),
      ),

      GoRoute(
        name: AppRouteName.agentLocatorComing,
        path: 'agentLocatorComing',
        pageBuilder: (context, state) => _pageBuilder(
          const AgentLocatorComingSoonScreen(),
          state,
        ),
      ),

      // Gift Package Routes
      GoRoute(
        name: AppRouteName.giftPackages,
        path: 'gift-packages',
        pageBuilder: (context, state) => _pageBuilder(
          GiftPackagesScreen(
            isGuest: state.extra is Map
                ? (state.extra as Map)['isGuest'] as bool? ?? false
                : false,
          ),
          state,
        ),
        routes: [
          GoRoute(
            name: AppRouteName.packageDetail,
            path: 'package/:id',
            pageBuilder: (context, state) => _pageBuilder(
              PackageDetailScreen(
                packageId: state.pathParameters['id']!,
                isGuest: state.extra != null && state.extra is Map
                    ? (state.extra as Map)['isGuest'] as bool? ?? false
                    : false,
              ),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.packageSearch,
            path: 'search',
            pageBuilder: (context, state) => _pageBuilder(
              PackageSearchScreen(
                isGuest: state.extra != null && state.extra is Map
                    ? (state.extra as Map)['isGuest'] as bool? ?? false
                    : false,
              ),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.merchantPackages,
            path: 'merchant/:bannerId',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                MerchantPackagesScreen(
                  banner: extra['bannerItem'] as BannerItem,
                  isGuest: extra['isGuestMode'] as bool,

                  // state.extra != null && state.extra is Map
                  //     ? (state.extra as Map)['isGuest'] as bool? ?? false
                  //     : false,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.giftRecipientDetails,
            path: 'recipient-details',
            pageBuilder: (context, state) {
              final args = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                RecipientDetailsScreen(
                  item: args['item'] as PackageItem,
                  quantity: args['quantity'] as int,
                  sellingPrice: args['sellingPrice'] as String,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.confirmPurchase,
            path: 'confirm-purchase',
            pageBuilder: (context, state) {
              final args = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                ConfirmPurchaseScreen(
                  item: args['item'] as PackageItem,
                  quantity: args['quantity'] as int,
                  recipientName: args['recipientName'] as String,
                  recipientPhone: args['recipientPhone'] as String,
                  sellingPrice: args['sellingPrice'] as String,
                  merchantName: args['merchantName'] as String,
                ),
                state,
              );
            },
          ),
        ],
      ),



      GoRoute(
        name: AppRouteName.guestFailure,
        path: 'guest-transfer-failure',
        pageBuilder: (context, state) => _pageBuilder(
          const GuestFailureScreen(),
          state,
        ),
      ),

      GoRoute(
        path: AppRouteName.balanceCheck,
        name: AppRouteName.balanceCheck,
        pageBuilder: (context, state) => _pageBuilder(
          MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => sl<BalanceCheckBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<RecentLinkedTransactionBloc>(),
              ),
            ],
            child: const BalanceCheckPage(),
          ),
          state,
        ),
        // builder: (context, state) => BlocProvider(
        //   create: (context) => sl<BalanceCheckBloc>(),
        //   child: const BalanceCheckPage(),
        // ),
      ),
      GoRoute(
        path: AppRouteName.transactionDetail,
        name: AppRouteName.transactionDetail,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            TransactionDetailPage(
              transaction: extra['transaction'] as Transaction,
            ),
            state,
          );
        },
      ),
      // Add this inside the _protectedRoutes list
      GoRoute(
        name: AppRouteName.addMoney,
        path: 'add-money',
        pageBuilder: (context, state) => _pageBuilder(
          _wrapWithAddMoneyBloc(
            AddMoneyScreen(
              defaultBankId: state.uri.queryParameters['bankId'],
              defaultAccountNumber: state.uri.queryParameters['accountNumber'],
            ),
          ),
          state,
        ),
      ),

      // merchant payment routes
      GoRoute(
        name: AppRouteName.merchantPayment,
        path: 'merchant-payment',
        pageBuilder: (context, state) => _pageBuilder(
          const MerchantPaymentScreen(),
          state,
        ),
        routes: [
          GoRoute(
            name: AppRouteName.merchantPaymentAddAmount,
            path: 'add-amount',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<MerchantPaymentBloc>(),
                  child: MerchantPaymentAddAmountPage(
                    merchantId: extra['merchantId'] as String,
                    merchantCode: extra['merchantCode'] as String,
                    merchantName: extra['merchantName'] as String,
                    // walletBalance: extra['walletBalance'] as double,
                  ),
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.merchantPaymentConfirmation,
            path: 'confirmation',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                MerchantPaymentConfirmationPage(
                  amount: extra['amount'] as String,
                  merchantId: extra['merchantId'] as String,
                  merchantCode: extra['merchantCode'] as String,
                  merchantName: extra['merchantName'] as String,
                  serviceCharge: extra['serviceCharge'] as double,
                  vat: extra['vat'] as double,
                  total: extra['total'] as double,
                  senderName: extra['senderName'] as String,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.merchantPaymentOtpConfirmation,
            path: 'merchant-otp-confirmation',
            pageBuilder: (context, state) {
              final args = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<MerchantPaymentBloc>(),
                  child: MerchantPaymentOtpConfirmation(
                    billRefNo: args['billRefNo'] as String,
                    amount: args['amount'] as String,
                    merchantName: args['merchantName'] as String,
                    merchantId: args['merchantId'] as String,
                    isMerchant: args['isMerchant'] as bool? ?? true,
                  ),
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.merchantPaymentSuccess,
            path: 'success',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                MerchantPaymentSuccessPage(
                  billRefNo: extra['billRefNo'] as String,
                  amount: extra['amount'] as String,
                  merchantName: extra['merchantName'] as String,
                  senderName: extra['senderName'] as String,
                  serviceCharge: extra['serviceCharge'] as double,
                  vat: extra['vat'] as double,
                  total: extra['total'] as double,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.merchantPaymentByQr,
            path: 'by-qr',
            pageBuilder: (context, state) => _pageBuilder(
              const MerchantPaymentByQrScreen(),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.merchantPaymentByQrAddAmount,
            path: 'add-amount-by-qr',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                MerchantPaymentByQrAddAmountPage(
                  merchantData: extra['merchantData'] as Map<String, dynamic>,
                  qrId: extra['qrId'] != null ? extra['qrId'] as String : null,
                ),
                state,
              );
            },
          ),
        ],
      ),
      // cash in cash out routes
      GoRoute(
        name: AppRouteName.cashInOut,
        path: 'cash-in-out',
        pageBuilder: (context, state) => _pageBuilder(
          const CashInOutPage(),
          state,
        ),
        routes: [
          GoRoute(
            name: AppRouteName.cashIn,
            path: 'cash-in',
            pageBuilder: (context, state) => _pageBuilder(
              CashInAddAmountPage(
                isUSD: state.extra is Map
                    ? (state.extra as Map)['isUSD'] as bool? ?? false
                    : false,
              ),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.cashInSuccess,
            path: 'cash-in-success',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                VoucherSuccessPage(
                  amount: extra['amount'] as String?, // Make it nullable
                  voucherCode: extra['voucherCode'] as String,
                  isCashIn: extra['isCashIn'] as bool? ?? true,
                  currency: extra['currency'] as String? ?? 'ETB',
                  isUSD: extra['isUSD'] as bool,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.cashInQrSuccess,
            path: 'cash-in-qr-success',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                QrSuccessPage(
                  amount: extra['amount'] as String?,
                  voucherCode: extra['voucherCode'] as String,
                  isCashIn: extra['isCashIn'] as bool? ?? true,
                  currency: extra['currency'] as String? ?? 'ETB',
                  isUSD: extra['isUSD'] as bool,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.cashOut,
            path: 'cash-out',
            pageBuilder: (context, state) => _pageBuilder(
              const CashOutMenuPage(),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.cashOutAgent,
            path: 'cash-out-agent',
            pageBuilder: (context, state) => _pageBuilder(
              BlocProvider(
                create: (context) => sl<CashInCashOutBloc>(),
                child: const CashOutAgentPage(),
              ),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.cashOutVoucher,
            path: 'cash-out-voucher',
            pageBuilder: (context, state) => _pageBuilder(
              const CashOutVoucherAddAmountPage(),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.cashOutQr,
            path: 'cash-out-qr',
            pageBuilder: (context, state) => _pageBuilder(
              const CashOutQrPage(),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.cashOutAgentAddAmount,
            path: 'cash-out-agent-add-amount',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              final agent = extra['agent'] as Agent;

              return _pageBuilder(
                CashOutAgentAddAmountPage(
                  id: agent.id,
                  agentName: agent.agentName,
                  agentCode: agent.agentCode,
                  showAgentContainer:
                      extra['showAgentContainer'] as bool? ?? false,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.cashOutConfirmation,
            path: 'cash-out-confirmation',
            pageBuilder: (context, state) => _pageBuilder(
              CashOutConfirmationPage(
                transactionData: state.extra as Map<String, dynamic>,
              ),
              state,
            ),
          ),
          GoRoute(
            name: AppRouteName.cashOutOtpConfirmation,
            path: 'cash-out-otp-confirmation',
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;
              return _pageBuilder(
                CashOutOtpConfirmationPage(
                  billRefNo: extra['billRefNo'] as String,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.cashOutSuccess,
            path: 'cash-out-success',
            pageBuilder: (context, state) => _pageBuilder(
              CashOutSuccessPage(
                data: state.extra as Map<String, dynamic>,
              ),
              state,
            ),
          ),
        ],
      ),

      GoRoute(
        path: AppRouteName.balanceHistory,
        name: AppRouteName.balanceHistory,
        pageBuilder: (context, state) => _pageBuilder(
          MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => sl<RecentLinkedTransactionBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<BalanceCheckBloc>(),
              ),
            ],
            child: const BalanceHistoryPage(),
          ),
          state,
        ),
      ),

      GoRoute(
        name: AppRouteName.linkAccountMenu,
        path: AppRouteName.linkAccountMenu,
        pageBuilder: (context, state) {
          // final extra = state.extra as Map<String, dynamic>;

          return _pageBuilder(
            _wrapWithLinkAccountBlock(
              const LinkAccountMenuScreen(),
            ),
            state,
          );
        },
        routes: [
          // for pended linked account
          GoRoute(
            name: AppRouteName.linkedBanks,
            path: AppRouteName.linkedBanks,
            pageBuilder: (context, state) {
              return _pageBuilder(
                _wrapWithLinkAccountBlock(
                  const LinkAccountPendingScreen(),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.linkBankList,
            path: AppRouteName.linkBankList,
            pageBuilder: (context, state) => _pageBuilder(
              MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => sl<AccountLinkBloc>(),
                  ),
                  BlocProvider(
                    create: (context) => sl<BankTransferBloc>(),
                  ),
                ],
                child: const LinkAccountBankListScreen(),
              ),
              state,
            ),
          ),

          GoRoute(
            name: AppRouteName.linkAccountForm,
            path: AppRouteName.linkAccountForm,
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                LinkAccountNumberFormScreen(
                  bank: extra['bank'] as Bank,
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.linkedAccountDetail,
            path: AppRouteName.linkedAccountDetail,
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                _wrapWithLinkAccountBlock(
                  LinkAccountCompleteScreen(
                    bankLogo: extra['bankLogo'] as String,
                    bankName: extra['bankName'] as String,
                    accountNumber: extra['accountNumber'] as String,
                    linkCode: extra['linkCode'] as String,
                    accountHolderName: extra['accountHolderName'] as String,
                    isFromPendingScreen: extra['isFromPendingScreen'] as bool,
                    requestedDate: extra['requestedDate'] as String,
                  ),
                ),
                state,
              );
            },
          ),

          // Money Request

          GoRoute(
            name: AppRouteName.moneyRequestMenuScreen,
            path: AppRouteName.moneyRequestMenuScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                const MoneyRequestMenuScreen(),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.sendMoneyRequestMemberLookupScreen,
            path: AppRouteName.sendMoneyRequestMemberLookupScreen,
            pageBuilder: (context, state) {
              final fromChatData = state.extra as Map<String, dynamic>?;
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<SendMoneyRequestMemberLookupBloc>(),
                  child: SendMoneyRequestMemberLookupScreen(
                    fromChatData: fromChatData,
                  ),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.myMoneyRequestListScreen,
            path: AppRouteName.myMoneyRequestListScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<MoneyRequestListBloc>(),
                  child: const MyMoneyRequestListScreen(),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.moneyRequestedListScreen,
            path: AppRouteName.moneyRequestedListScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<MoneyRequestListBloc>(),
                  child: const MoneyRequestedListScreen(),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.moneyRequestCheckAmountScreen,
            path: AppRouteName.moneyRequestCheckAmountScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => sl<MoneyRequestDetailBloc>(),
                    ),
                    BlocProvider(
                      create: (context) => sl<WalletBalanceBloc>(),
                    ),
                    BlocProvider(
                      create: (context) => sl<WalletTransferBloc>(),
                    ),
                  ],
                  child: MoneyRequestCheckAmountScreen(
                    moneyRequest: state.extra as MoneyRequestEntity,
                  ),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.moneyRequestConfirmRequestScreen,
            path: AppRouteName.moneyRequestConfirmRequestScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<MoneyRequestDetailBloc>(),
                  child: MoneyRequestConfirmRequestScreen(
                    moneyRequest: state.extra as MoneyRequestEntity,
                  ),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.sendMoneyRequestAddMoneyScreen,
            path: AppRouteName.sendMoneyRequestAddMoneyScreen,
            pageBuilder: (context, state) {
              final extraData = state.extra;
              MemberLookupEntity member;
              Map<String, dynamic>? fromChatData;

              if (extraData is Map<String, dynamic>) {
                member = extraData['member'] as MemberLookupEntity;
                fromChatData =
                    extraData['fromChatData'] as Map<String, dynamic>?;
              } else {
                // Fallback for old navigation style
                member = extraData as MemberLookupEntity;
                fromChatData = null;
              }

              return _pageBuilder(
                MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => sl<SendMoneyRequestAddMoneyBloc>(),
                    ),
                    BlocProvider(
                      create: (context) =>
                          sl<SendRequestMoneyConfirmRequestBloc>(),
                    ),
                    BlocProvider(
                      create: (context) => sl<ChatBloc>(),
                    ),
                  ],
                  child: SendMoneyRequestAddMoneyScreen(
                    member: member,
                    fromChatData: fromChatData,
                  ),
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.sendMoneyRequestSuccessScreen,
            path: AppRouteName.sendMoneyRequestSuccessScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                SendMoneyRequestSuccessScreen(
                  moneyRequest: state.extra as MoneyRequestEntity,
                ),
                state,
              );
            },
          ),

          GoRoute(
            name: AppRouteName.requestDetailScreen,
            path: AppRouteName.requestDetailScreen,
            pageBuilder: (context, state) {
              return _pageBuilder(
                BlocProvider(
                  create: (context) => sl<MoneyRequestDetailBloc>(),
                  child: MoneyRequestDetailScreen(
                    moneyRequest: state.extra as MoneyRequestEntity,
                  ),
                ),
                state,
              );
            },
          ),
        ],
      ),

      // Add this route under the main routes section
      GoRoute(
        name: AppRouteName.agentLocator,
        path: 'agent-locator',
        pageBuilder: (context, state) => _pageBuilder(
          const AgentLocatorPage(),
          state,
        ),
      ),

      GoRoute(
        name: AppRouteName.topUpMenu,
        path: AppRouteName.topUpMenu,
        pageBuilder: (context, state) => _pageBuilder(
          const TopUpMenuScreen(),
          state,
        ),
        routes: [
          GoRoute(
            name: AppRouteName.topUpContact,
            path: AppRouteName.topUpContact,
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => sl<WalletTransferBloc>(),
                    ),
                  ],
                  child: TopUpContactScreen(
                    provider: extra['provider'] as ProvidersEntity,
                  ),
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.topUpAddAmount,
            path: AppRouteName.topUpAddAmount,
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => sl<TopUpBloc>(),
                    ),
                    BlocProvider(
                      create: (context) => sl<WalletTransferBloc>(),
                    ),
                  ],
                  child: TopUpAddAmounScreen(
                    phoneNumber: extra['phoneNumber'] as String,
                    provider: extra['provider'] as ProvidersEntity,
                  ),
                ),
                state,
              );
            },
          ),
          GoRoute(
            name: AppRouteName.topUpSuccess,
            path: AppRouteName.topUpSuccess,
            pageBuilder: (context, state) {
              final extra = state.extra as Map<String, dynamic>;

              return _pageBuilder(
                MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => sl<TopUpBloc>(),
                    ),
                  ],
                  child: TopUpSuccessScreen(
                    totalAmount: extra['totalAmount'] as String,
                    operatorCode: extra['operatorCode'] as String,
                    phoneNumber: extra['phoneNumber'] as String,
                    topUpSuccessDataEntity: extra['topUpSuccessDataEntity']
                        as TopUpSuccessDataEntity,
                  ),
                ),
                state,
              );
            },
          ),
        ],
      ),

      GoRoute(
        name: AppRouteName.qrOptions,
        path: AppRouteName.qrOptions,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<ParseQrBloc>(),
              child: QrOptionsPage(
                isUSD: extra['isUSD'] != null ? extra['isUSD'] as bool : false,
              ),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.utilityScreen,
        path: AppRouteName.utilityScreen,
        pageBuilder: (context, state) {
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<UtilityBloc>(),
              child: const UtilityScreen(),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.utilityWebView,
        path: AppRouteName.utilityWebView,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<UtilityBloc>(),
              child: UtilityWebview(
                url: extra['url'] as String,
                appName: extra['appName'] as String,
              ),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.utilitySuccess,
        path: AppRouteName.utilitySuccess,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<UtilityBloc>(),
              child: UtilitySuccessScreen(
                data: extra['success'] as UtilitySuccessDataEntity,
              ),
            ),
            state,
          );
        },
      ),
      //miniapp
      GoRoute(
        name: AppRouteName.miniappScreen,
        path: AppRouteName.miniappScreen,
        pageBuilder: (context, state) {
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<MiniappBloc>(),
              child: const MiniAppScreen(),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.miniappWebView,
        path: AppRouteName.miniappWebView,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<MiniappBloc>(),
              child: MiniappWebview(
                url: extra['url'] as String,
                appName: extra['appName'] as String,
              ),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.miniappConfirmation,
        path: AppRouteName.miniappConfirmation,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<MiniappBloc>(),
              child: MiniappConfirmationScreen(
                data: extra['confirm'] as CreateOrderMiniappDataEntity,
              ),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.miniappSuccess,
        path: AppRouteName.miniappSuccess,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<MiniappBloc>(),
              child: MiniappSuccessScreen(
                data: extra['success'] as MiniappSuccessDataEntity,
              ),
            ),
            state,
          );
        },
      ),

      GoRoute(
        name: AppRouteName.miniStatements,
        path: AppRouteName.miniStatements,
        pageBuilder: (context, state) {
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<MiniStatementBloc>(),
              child: const MiniStatementsMenuScreen(),
            ),
            state,
          );
        },
      ),

      /*
      UtilitySuccessScreen
         GoRoute(
        name: AppRouteName.utilityConfirmation,
        path: AppRouteName.utilityConfirmation,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;



          debugPrint("iiiis esxtra ${extra['confirm'].runtimeType}");
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<UtilityBloc>(),
              child: UtilityConfirmationScreen(
                data: extra['confirm'] as CreateOrderUtilityDataEntity,
              ),
            ),
            state,
          );
        },
      ),
   */
    ],
  ),
];

class ProtectedRoute extends StatelessWidget {
  const ProtectedRoute({required this.child, super.key});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        final authBloc = context.read<AuthBloc>();
        if (!authBloc.isAuthenticated) {
          context.goNamed(AppRouteName.signIn);
        }
      },
      child: child,
    );
  }
}

Widget _wrapWithProfile(Widget page) {
  return BlocProvider(
    create: (_) => sl<ProfileBloc>(),
    child: page,
  );
}

Widget _wrapWithRepayment(Widget page) {
  return BlocProvider(
    create: (_) => sl<RepaymentBloc>(),
    child: page,
  );
}

Widget _wrapWithLinkAccountBlock(Widget page) {
  return BlocProvider(
    create: (_) => sl<AccountLinkBloc>(),
    child: page,
  );
}

Widget _wrapWithAddMoneyBloc(Widget page) {
  return BlocProvider(
    create: (_) => sl<AddMoneyBloc>(),
    child: page,
  );
}
