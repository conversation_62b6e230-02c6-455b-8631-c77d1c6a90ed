import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_drop_balance.dart';
import 'package:cbrs/core/common/widgets/custom_number_keyboard.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_wallet_balance.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/transfer_limit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class CurrencyInputWidget extends StatefulWidget {
  const CurrencyInputWidget(
      {required this.controller,
      required this.title,
      required this.subtitle,
      required this.onContinue,
      super.key,
      this.showExchangeAmount = false,
      this.isLoading = false,
      this.buttonText = 'Continue',
      this.transactionType,
      this.resetBalance,
      this.showBalanceSwitching = false,
      this.customBalance,
      this.customWalletType,
      this.onWalletSwitched,
      this.header,
      this.hasCustomWalletDisplay = false,
      this.walletBalanceLabel = 'Wallet Balance',
      this.exchangeAmount = '',
      this.checkTransferLimit = true,
      this.fetchTransferLimits = true,
      this.displayBalances = true});
  final CurrencyInputController controller;
  final String title;
  final bool hasCustomWalletDisplay;
  final double? customBalance;
  final String? customWalletType;
  final bool checkTransferLimit;
  final bool displayBalances;
  final String subtitle;
  final String? transactionType;
  final bool showExchangeAmount;
  final dynamic exchangeAmount;
  final VoidCallback onContinue;
  final bool isLoading;
  final String buttonText;
  final bool showBalanceSwitching;
  final String walletBalanceLabel;

  final bool fetchTransferLimits;

  final Widget? header;

  final void Function(String)? resetBalance;

  final void Function(String)? onWalletSwitched;

  @override
  State<CurrencyInputWidget> createState() => _CurrencyInputWidgetState();
}

class _CurrencyInputWidgetState extends State<CurrencyInputWidget> {
  final bool _isProcessing = false;
  TransferLimit? _transferLimit;

  double? newBalance;
  String? newWalletType;

  void _showMaxLimitToast() {
    if (mounted) {
      CustomToastification(
        context,
        message: 'Amount exceeds maximum transfer limit',
      );
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.checkTransferLimit) _fetchTransferLimits();

    // Set up the toast callback for the controller
    _setupToastCallback();
  }

  void _setupToastCallback() {
    // We need to create a new controller instance with the callback
    // Since we can't modify the existing controller, we'll use a different approach
    // by listening to the exceedsTransferLimitNotifier directly
    widget.controller.exceedsTransferLimitNotifier
        .addListener(_onLimitExceededChanged);
  }

  bool _previousExceedsLimit = false;

  void _onLimitExceededChanged() {
    final currentExceedsLimit =
        widget.controller.exceedsTransferLimitNotifier.value;

    // Show toast only when limit is first exceeded (transition from false to true)
    if (currentExceedsLimit && !_previousExceedsLimit) {
      _showMaxLimitToast();
    }

    _previousExceedsLimit = currentExceedsLimit;
  }

  @override
  void dispose() {
    // Clean up the listener
    widget.controller.exceedsTransferLimitNotifier
        .removeListener(_onLimitExceededChanged);
    super.dispose();
  }

  void _fetchTransferLimits() {
    if (widget.transactionType != null && widget.fetchTransferLimits) {
      context.read<TransactionBloc>().add(
            FetchTransferLimitEvent(
              transactionType: widget.transactionType!,
              currency: newWalletType != null
                  ? newWalletType == 'USD'
                      ? 'USD'
                      : 'ETB'
                  : widget.controller.currencyType == CurrencyType.usd
                      ? 'USD'
                      : 'ETB',
            ),
          );
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // padding: const EdgeInsets.only(bottom: 10),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.header != null)
                      widget.header!
                    else if (widget.title != '')
                      _buildHeader(),
                    Container(
                      // color: Colors.red,
                      height: 8,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (widget.showBalanceSwitching)
                          CustomDropBalance(
                            selectedWallet:
                                //  widget.hasCustomWalletDisplay
                                // ? GlobalVariable.currentlySelectedWallet ??
                                //     'ETB'
                                // :

                                widget.controller.currencyType ==
                                        CurrencyType.usd
                                    ? 'USD'
                                    : 'ETB',
                            textColor: Colors.black,
                            bgColor: Colors.white,
                            onTap: () {},
                            fetchTransferLimits: (balance, selectedWallet) {
                              setState(() {
                                newBalance = balance;
                                newWalletType = selectedWallet;
                              });

                              if (widget.resetBalance != null) {
                                widget.resetBalance!(selectedWallet);
                              }
                              _fetchTransferLimits();
                            },
                            hasCustomOnTap: true,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.08),
                                blurRadius: 24,
                              ),
                            ],
                          ),
                        SizedBox(
                          height: 16.h,
                        ),
                        _buildAmountInputSection(),
                        _buildKeypadAndButton(),
                        const SizedBox(
                          height: 12,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

/*
  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(maxHeight: constraints.maxHeight),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (widget.title != '') _buildHeader(),
                      // Expanded(
                      //   child: _buildAmountInputSection(),
                      // ),
                      _buildKeypadAndButton(),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
*/
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      child: CustomPageHeader(
        pageTitle: widget.title,
        description: widget.subtitle,
      ),
    );
  }

  Widget _buildAmountInputSection() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (widget.showExchangeAmount &&
              widget.controller.currencyType == CurrencyType.usd) ...[
            _buildExchangeAmount(),
            SizedBox(
              height: MediaQuery.sizeOf(context).height * 0.015,
            ),
          ],
          _buildAmountTextField(),
          _buildMinimumAmountIndicator(context),
          SizedBox(height: 16.h),
          if (widget.displayBalances)
          _buildWalletBalance(),
          SizedBox(height: 32.h),
        ],
      ),
    );
  }

  Widget _buildWalletBalance() {
    return CustomWalletBalance(
      balanceLabel: widget.walletBalanceLabel,
      customBalance: newBalance ?? widget.customBalance,
      customWalletType: newWalletType ?? widget.customWalletType,
    );
  }

  Widget _buildMinimumAmountIndicator(BuildContext context) {
    return BlocBuilder<TransactionBloc, TransactionState>(
      builder: (context, state) {
        if (state is TransactionLoading) return const _BalanceShimmer();

        //TransactionError  TransferLimitLoaded
        var minAmount = '';
        var maxAmount = '';

        if (state is TransferLimitLoaded) {
          _transferLimit = state.transferLimit;

          // Store limit values in controller
          final minLimit = state.transferLimit.minTransferLimit;
          final maxLimit = state.transferLimit.maxTransferLimit;

          // Set controller's limit properties
          widget.controller.minTransferLimit = minLimit;
          widget.controller.maxTransferLimit = maxLimit;

          // Get current amount for validation
          final currentAmount = widget.controller.numericAmount;

          // Update controller's notifier for max limit
          widget.controller.exceedsTransferLimitNotifier.value =
              currentAmount > maxLimit;

          // The isValidNotifier will be updated in the controller's _handleTextChange method
          // Force a text change event to update validation status

          // widget.controller.textController.notifyListeners();
          WidgetsBinding.instance.addPostFrameCallback((_) {
            widget.controller.textController.notifyListeners();
          });

          minAmount = newWalletType != null
              ? newWalletType == 'USD'
                  ?

                  // widget.controller.currencyType == CurrencyType.usd
                  '\$${state.transferLimit.minTransferLimit}'
                  : 'ETB ${state.transferLimit.minTransferLimit}'
              : widget.controller.currencyType == CurrencyType.usd
                  ? '\$${state.transferLimit.minTransferLimit}'
                  : '${state.transferLimit.minTransferLimit} ETB';

          maxAmount = widget.hasCustomWalletDisplay
              ? (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
                  //widget.controller.currencyType == CurrencyType.usd
                  ? '\$${state.transferLimit.maxTransferLimit}'
                  : '${state.transferLimit.maxTransferLimit} ETB'
              : widget.controller.currencyType == CurrencyType.usd
                  ? '\$${state.transferLimit.maxTransferLimit}'
                  : '${state.transferLimit.maxTransferLimit} ETB';
        }

        return minAmount != ''
            ? Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.black.withOpacity(0.4),
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Minimum Amount:',
                        style: GoogleFonts.outfit(
                          color: Colors.black.withOpacity(0.4),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        ' ${minAmount.split('.').first} ',
                        style: GoogleFonts.outfit(
                          color: Colors.black.withOpacity(0.4),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildAmountTextField() {
    return TextField(
      controller: widget.controller.textController,
      readOnly: true,
      textAlign: TextAlign.center,
      style: GoogleFonts.outfit(
        fontSize: 40.sp,
        fontWeight: FontWeight.w800,
        color: Colors.black,
      ),
      decoration: InputDecoration(
        border: InputBorder.none,
        hintText:

            //  widget.hasCustomWalletDisplay
            //     ? (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
            //         // widget.controller.currencyType == CurrencyType.usd
            //         ? r'$0.00'
            //         : '0.00 ETB'
            //     :
            widget.controller.currencyType == CurrencyType.usd
                ? r'$0.00'
                : '0.00 ETB',
        hintStyle: GoogleFonts.outfit(
          fontSize: 40.sp,
          color: Colors.grey,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget _buildExchangeAmount() {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.controller.showSecondaryAmountNotifier,
      builder: (context, showSecondary, _) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 16.w),
          child: Column(
            children: [
              if (showSecondary) ...[
                SizedBox(height: 8.h),
                ValueListenableBuilder<String>(
                  valueListenable: widget.controller.secondaryAmountNotifier,
                  builder: (context, secondaryAmount, _) {
                    return Text(
                      _formatSecondaryAmount(secondaryAmount),
                      style: GoogleFonts.outfit(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                        // letterSpacing: 0.5,
                      ),
                    );
                  },
                ),
              ],
              Text(
                widget.controller.currencyType == CurrencyType.usd
                    ? 'Exchange Rate: 1 USD = ETB ${widget.exchangeAmount}'
                    : '',
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  color: showSecondary ? Colors.black54 : Colors.black,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatSecondaryAmount(String secondaryAmount) {
    if (secondaryAmount.isEmpty) {
      return widget.hasCustomWalletDisplay
          ? (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
              //  widget.controller.currencyType == CurrencyType.usd
              ? '0.00 ETB'
              : r'$0.00'
          : widget.controller.currencyType == CurrencyType.usd
              ? '0.00 ETB'
              : r'$0.00';
    }

    try {
      // Extract numeric value from the string (removing currency symbols)
      final regExp = RegExp('[0-9,.]+');
      final match = regExp.firstMatch(secondaryAmount);
      if (match != null) {
        final numStr = match.group(0) ?? '';
        final amount = double.parse(numStr.replaceAll(',', ''));

        // Format with proper thousand separators
        final formattedAmount = _formatWithCommas(amount.toStringAsFixed(2));

        // Add currency symbol in the correct position
        return widget.hasCustomWalletDisplay
            ? (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
                // widget.controller.currencyType == CurrencyType.usd
                ? '$formattedAmount ETB'
                : '\$$formattedAmount'
            : widget.controller.currencyType == CurrencyType.usd
                ? '$formattedAmount ETB'
                : '\$$formattedAmount';
      }
    } catch (e) {
      // If parsing fails, return the original string
    }

    return secondaryAmount;
  }

  String _formatWithCommas(String number) {
    if (number.contains('.')) {
      final parts = number.split('.');
      return '${_insertCommasIntoWholePart(parts[0])}.${parts[1]}';
    }
    return _insertCommasIntoWholePart(number);
  }

  String _insertCommasIntoWholePart(String wholePart) {
    final characters = wholePart.split('').reversed.toList();
    final withCommas = <String>[];

    for (var i = 0; i < characters.length; i++) {
      if (i > 0 && i % 3 == 0) {
        withCommas.add(',');
      }
      withCommas.add(characters[i]);
    }

    return withCommas.reversed.join();
  }

  Widget _buildKeypadAndButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: const Color(0xFFF9F9F9),
      ),
      child: Column(
        children: [
          CustomNumberKeyboard(
            onKeyPressed:
                widget.isLoading ? (data) {} : widget.controller.onKeyPressed,
            // useBackspace: false,
            fontSize: 22.sp,
          ),
          SizedBox(height: 12.h),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: ValueListenableBuilder<bool>(
              valueListenable: widget.controller.isValidNotifier,
              builder: (context, isValid, _) {
                return SafeArea(
                  child: CustomRoundedBtn(
                    btnText:
                        widget.isLoading ? 'Processing...' : widget.buttonText,
                    onTap: (isValid && !widget.isLoading)
                        ? widget.onContinue
                        : null,
                    isLoading: widget.isLoading,
                    bgColor: (isValid && !widget.isLoading)
                        ? Theme.of(context).primaryColor
                        : Colors.grey,
                    isBtnActive: isValid && !widget.isLoading,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _BalanceShimmer extends StatelessWidget {
  const _BalanceShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 30,
        width: 130,
        decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}
