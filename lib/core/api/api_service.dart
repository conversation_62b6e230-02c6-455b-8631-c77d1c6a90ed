// import 'package:approov_service_flutter_httpclient/approov_service_flutter_httpclient.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/services/injection_container.dart' as sl;
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/routes/route.dart' show router;
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class ApiService {
  ApiService({required AuthLocalDataSource localDataSource})
      : _localDataSource = localDataSource {
    _dio = _createDio();
  }
  late final Dio _dio;
  final AuthLocalDataSource _localDataSource;
  final Connectivity _connectivity = Connectivity();

  Dio _createDio() {
    final dio = Dio(
      BaseOptions(
        baseUrl: _getBaseUrl(),
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
        sendTimeout: const Duration(seconds: 60),
        headers: _getDefaultHeaders(),
        validateStatus: (status) => true,
      ),
    );

    // // Add PrettyDioLogger for pretty logging
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        maxWidth: 120,
      ),
    );

    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   return ApproovHttpClient(ApiConstants.approovConfig);
    // };

    return dio;
  }

  String _getBaseUrl() {
    return ApiConstants.baseUrl;
    // if (kReleaseMode) {
    //   return ApiConstants.baseUrl;
    // } else if (kProfileMode) {
    //   return ApiConstants.baseUrlStaging;
    // }
    // return ApiConstants.baseUrlDev;
  }

  Map<String, String> _getDefaultHeaders() {
    return {
      ApiConstants.contentType: ApiConstants.applicationJson,
      ApiConstants.accept: ApiConstants.applicationJson,
      ApiConstants.apiKey: ApiConstants.apiKeyValue,
    };
  }

  Future<Map<String, String>> _getHeaders({bool requiresAuth = true}) async {
    final headers = _getDefaultHeaders();

    if (requiresAuth) {
      final token = await _localDataSource.getAuthToken();

      if (token != null) {
        headers[ApiConstants.authorization] = '${ApiConstants.bearer} $token';
      }
    }

    return headers;
  }

  Future<bool> _isConnected() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<Result<T>> _executeRequest<T>(
    Future<Response> Function() request,
    T Function(dynamic data) parser,
  ) async {
    debugPrint('Post request  _executeRequest $request');

    try {
      // Check network connectivity
      if (!await _isConnected()) {
        return Result.failure(ApiError.network());
      }

      final response = await request();

      return _handleResponse(response, parser);
    } on DioException catch (e) {
      debugPrint('catrch diexception error');

      return Result.failure(_handleDioError(e));
    } catch (e) {
      debugPrint('catrch error');
      return Result.failure(
        ApiError(
          message: e.toString(),
        ),
      );
    }
  }

  Result<T> _handleResponse<T>(
    Response response,
    T Function(dynamic data) parser,
  ) {
    final statusCode = response.statusCode ?? 500;

    final newAccessToken = response.headers['x-new-token']?.first;

    if (newAccessToken != null) {
      _localDataSource.saveAuthToken(newAccessToken);
    }

    if (statusCode >= 200 && statusCode < 300) {
      try {
        final parsedData = parser(response.data);
        return Result.success(parsedData);
      } catch (e) {
        return Result.failure(
          ApiError(
            message: 'Failed to parse response: $e',
            statusCode: statusCode,
          ),
        );
      }
    }

    // Handle error responses
    var message = 'Unknown error occurred';
    Map<String, dynamic>? errors;

    if (response.data is Map) {
      message = response.data['message'] as String? ?? message;
      errors = response.data['errors'] as Map<String, dynamic>?;
    }

    // Handle specific status codes

    debugPrint('statusCode for comparing $statusCode');

    switch (statusCode) {
      case 401:
        _handleUnauthorized();
        return Result.failure(ApiError.unauthorized());
      case 400:
        return Result.failure(
          ApiError(
            message: message,
            statusCode: statusCode,
            errors: errors,
            type: ErrorType.validation,
          ),
        );
      case 404:
        return Result.failure(
          ApiError(
            message: message,
            statusCode: statusCode,
            type: ErrorType.server,
          ),
        );
      default:
        return Result.failure(ApiError.server(message, statusCode));
    }
  }

  ApiError _handleDioError(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout) {
      return ApiError.timeout();
    }

    if (error.type == DioExceptionType.connectionError) {
      return ApiError.network();
    }

    if (error.response != null) {
      return _extractErrorFromResponse(error.response!);
    }

    return ApiError(
      message: error.message ?? 'An unexpected error occurred',
    );
  }

  ApiError _extractErrorFromResponse(Response response) {
    final statusCode = response.statusCode ?? 500;
    var message = 'Unknown error occurred';
    Map<String, dynamic>? errors;

    if (response.data is Map) {
      message = response.data['message'] as String? ?? message;
      errors = response.data['errors'] as Map<String, dynamic>?;
    }

    switch (statusCode) {
      case 401:
        _handleUnauthorized();
        return ApiError.unauthorized();
      case 400:
        return ApiError(
          message: message,
          statusCode: statusCode,
          errors: errors,
          type: ErrorType.validation,
        );
      default:
        return ApiError.server(message, statusCode);
    }
  }

  Future<void> _handleUnauthorized() async {
    try {
      // Clear auth token
      await _localDataSource.clearAuthToken();

      final navigationService = sl.sl<NavigationService>();
      await navigationService.handleRedirect(AppRouteName.tokenDeviceLogin);

      // Get the current context from router for navigation
      final context = router.routerDelegate.navigatorKey.currentContext;
      if (context != null && context.mounted) {
        context.go(AppRouteName.tokenDeviceLogin);
      }
    } catch (e) {
      debugPrint('Error handling unauthorized state: $e');
    }
  }

  // API Methods
  Future<Result<T>> get<T>(
    String path, {
    required T Function(dynamic data) parser,
    Map<String, dynamic>? queryParameters,
    bool requiresAuth = true,
  }) async {
    return _executeRequest(
      () async {
        final headers = await _getHeaders(requiresAuth: requiresAuth);
        return _dio.get(
          path,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
      },
      parser,
    );
  }

  Future<Result<T>> post<T>(
    String path, {
    required T Function(dynamic data) parser,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool requiresAuth = true,
    Options? options,
  }) async {
    return _executeRequest(
      () async {
        final headers = await _getHeaders(requiresAuth: requiresAuth);
        return _dio.post(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options ?? Options(headers: headers),
        );
      },
      parser,
    );
  }

  Future<Result<T>> patch<T>(
    String path, {
    required T Function(dynamic data) parser,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool requiresAuth = true,
    Options? options,
  }) async {
    return _executeRequest(
      () async {
        final headers = await _getHeaders(requiresAuth: requiresAuth);
        return _dio.patch(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options ?? Options(headers: headers),
        );
      },
      parser,
    );
  }

  Future<Result<T>> put<T>(
    String path, {
    required T Function(dynamic data) parser,
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? fields,
    Map<String, dynamic>? queryParameters,
    bool requiresAuth = true,
  }) async {
    return _executeRequest(
      () async {
        final headers = await _getHeaders(requiresAuth: requiresAuth);

        // If files are provided, create FormData for multipart upload
        dynamic requestData = data;
        if (files != null && files.isNotEmpty) {
          final formData = FormData();

          // Add all files
          files.forEach((key, file) {
            formData.files.add(MapEntry(key, file));
          });

          // Add all text fields if provided
          if (fields != null) {
            fields.forEach((key, value) {
              formData.fields.add(MapEntry(key, value.toString()));
            });
          }

          requestData = formData;
          // Remove content type to let Dio set the correct boundary
          headers.remove(ApiConstants.contentType);
        }

        debugPrint(' 👌urll path $path');
        debugPrint('requestData path ${requestData.runtimeType}');

        return _dio.put(
          path,
          data: requestData,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
      },
      parser,
    );
  }

  Future<Result<T>> delete<T>(
    String path, {
    required T Function(dynamic data) parser,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool requiresAuth = true,
  }) async {
    return _executeRequest(
      () async {
        final headers = await _getHeaders(requiresAuth: requiresAuth);
        return _dio.delete(
          path,
          data: data,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
      },
      parser,
    );
  }

  /// Uploads one or more files using multipart/form-data
  ///
  /// [path] - API endpoint path
  /// [files] - Map of field names to file data (can include multiple files)
  /// [fields] - Additional text fields to include in the request
  /// [requiresAuth] - Whether this request requires authentication
  /// [parser] - Function to parse the response data
  Future<Result<T>> uploadFiles<T>(
    String path, {
    required Map<String, MultipartFile> files,
    required T Function(dynamic data) parser,
    Map<String, dynamic>? fields,
    bool requiresAuth = true,
  }) async {
    return _executeRequest(
      () async {
        final headers = await _getHeaders(requiresAuth: requiresAuth);

        // Create form data
        final formData = FormData();

        // Add all files
        files.forEach((key, file) {
          formData.files.add(MapEntry(key, file));
        });

        // Add all text fields if provided
        if (fields != null) {
          fields.forEach((key, value) {
            formData.fields.add(MapEntry(key, value.toString()));
          });
        }

        // Set content type to multipart/form-data
        headers.remove(
          ApiConstants.contentType,
        ); // Let Dio set the correct boundary

        return _dio.post(
          path,
          data: formData,
          options: Options(headers: headers),
        );
      },
      parser,
    );
  }
}
