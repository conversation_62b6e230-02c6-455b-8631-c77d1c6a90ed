import 'package:cbrs/features/cash_in_out/domain/usecases/cash_in_cash_out_check_transfer_rules_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/confirm_payment_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/cash_out_resend_otp_usecase.dart';
import 'package:cbrs/features/cash_in_out/domain/usecases/verify_otp_usecase.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/generate_voucher_usecase.dart';
import '../../domain/usecases/search_agent_usecase.dart';
import '../../domain/usecases/generate_bill_usecase.dart';
import '../../domain/usecases/parse_agent_qr_usecase.dart';
import 'cash_in_cash_out_event.dart';
import 'cash_in_cash_out_state.dart';

class CashInCashOutBloc extends Bloc<CashInCashOutEvent, CashInCashOutState> {
  final GenerateVoucherUseCase _generateVoucherUseCase;
  final SearchAgentUseCase _searchAgentUseCase;
  final GenerateBillUseCase _generateBillUseCase;
  final CashInCashInCheckTransferRulesUseCase _checkTransferRulesUseCase;
  final ConfirmPaymentUseCase _confirmPaymentUseCase;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final CashOutResendOtpUseCase _resendOtpUseCase;
  final ParseAgentQrUseCase _parseAgentQrUseCase;

  CashInCashOutBloc({
    required GenerateVoucherUseCase generateVoucherUseCase,
    required SearchAgentUseCase searchAgentUseCase,
    required GenerateBillUseCase generateBillUseCase,
    required CashInCashInCheckTransferRulesUseCase checkTransferRulesUseCase,
    required ConfirmPaymentUseCase confirmPaymentUseCase,
    required VerifyOtpUseCase verifyOtpUseCase,
    required CashOutResendOtpUseCase resendOtpUseCase,
    required ParseAgentQrUseCase parseAgentQrUseCase,
  })  : _generateVoucherUseCase = generateVoucherUseCase,
        _searchAgentUseCase = searchAgentUseCase,
        _generateBillUseCase = generateBillUseCase,
        _checkTransferRulesUseCase = checkTransferRulesUseCase,
        _confirmPaymentUseCase = confirmPaymentUseCase,
        _verifyOtpUseCase = verifyOtpUseCase,
        _resendOtpUseCase = resendOtpUseCase,
        _parseAgentQrUseCase = parseAgentQrUseCase,
        super(CashInCashOutInitial()) {
    on<SearchAgentRequested>(_onSearchAgentRequested);
    on<GenerateVoucherRequested>(_onGenerateVoucherRequested);
    on<GenerateBillRequested>(_onGenerateBillRequested);
    on<CheckTransferRulesRequested>(_onCheckTransferRulesRequested);
    on<ConfirmPaymentRequested>(_onConfirmPaymentRequested);
    on<VerifyOtpRequested>(_onVerifyOtpRequested);
    on<ResendOtpRequested>(_onResendOtpRequested);
    on<ParseAgentQr>(_onParseAgentQr);
    on<ResetAgentState>((event, emit) => emit(CashInCashOutInitial()));
  }

  Future<void> _onParseAgentQr(
    ParseAgentQr event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    final result = await _parseAgentQrUseCase(event.qrString);

    result.fold(
      (failure) => emit(CashInCashOutFailure(failure.message)),
      (agent) => emit(AgentQrParsed(agent)),
    );
  }

  Future<void> _onSearchAgentRequested(
    SearchAgentRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    try {
      final agent = await _searchAgentUseCase(event.agentId);
      if (agent != null) {
        emit(AgentSearchSuccess(agent));
      } else {
        emit(const CashInCashOutFailure('Agent not found'));
      }
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }

  Future<void> _onGenerateVoucherRequested(
    GenerateVoucherRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    try {
      final voucher = await _generateVoucherUseCase(
        amount: event.amount,
        isCashIn: event.isCashIn,
        currency: event.currency,
      );
      emit(VoucherGenerationSuccess(voucher));
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }

  Future<void> _onCheckTransferRulesRequested(
    CheckTransferRulesRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    try {
      final rules = await _checkTransferRulesUseCase.call(
        amount: event.amount,
        currency: event.currency,
        productType: 'cash_out',
      );

      final total = rules.amount + rules.serviceCharge + rules.vat;

      emit(TransferRulesCheckSuccess(
        authorizationType: rules.authorizationType,
        amount: rules.amount,
        serviceCharge: rules.serviceCharge,
        vat: rules.vat,
        total: total,
      ));
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }

  Future<void> _onGenerateBillRequested(
    GenerateBillRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    try {
      final bill = await _generateBillUseCase(
        agentId: event.agentId,
        amount: event.amount,
        transactionType: event.transactionType,
        currency: event.currency,
      );
      emit(BillGenerationSuccess(bill));
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }

  Future<void> _onConfirmPaymentRequested(
    ConfirmPaymentRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    try {
      final result = await _confirmPaymentUseCase(
        pin: event.pin,
        billRefNo: event.billRefNo,
        transactionType: event.transactionType,
      );

      emit(PaymentConfirmationSuccess(result));
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }

  Future<void> _onVerifyOtpRequested(
    VerifyOtpRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    emit(CashInCashOutLoading());

    try {
      final success = await _verifyOtpUseCase(
        billRefNo: event.billRefNo,
        otpFor: 'cash_out',
        otpCode: event.otpCode,
      );

      if (success) {
        emit(OtpVerificationSuccess(
          billRefNo: event.billRefNo,
          requiresPin: true, // For cash out, PIN is always required after OTP
        ));
      }
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }

  Future<void> _onResendOtpRequested(
    ResendOtpRequested event,
    Emitter<CashInCashOutState> emit,
  ) async {
    try {
      await _resendOtpUseCase(
        billRefNo: event.billRefNo,
        otpFor: 'cash_out',
      );
      emit(OtpResendSuccess());
    } catch (e) {
      emit(CashInCashOutFailure(e.toString()));
    }
  }
}
