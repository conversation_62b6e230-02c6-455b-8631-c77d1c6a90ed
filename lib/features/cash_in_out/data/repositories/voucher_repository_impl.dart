import '../../domain/repositories/voucher_repository.dart';
import '../../domain/entities/voucher.dart';
import '../models/voucher_model.dart';
import '../../../../core/errors/failures.dart';
import '../../../../features/auth/data/datasources/auth_local_datasource.dart';
import '../datasources/voucher_remote_datasource.dart';
import 'package:dartz/dartz.dart';

class VoucherRepositoryImpl implements VoucherRepository {
  final VoucherRemoteDataSource remoteDataSource;
  final AuthLocalDataSource authLocalDataSource;

  VoucherRepositoryImpl({
    required this.remoteDataSource,
    required this.authLocalDataSource,
  });

  @override
  Future<Either<Failure, Voucher>> generateVoucher({
    double? amount,
    required bool isCashIn,
    String currency = 'ETB',
  }) async {
    try {
      final voucher = await remoteDataSource.generateVoucher(
        amount: amount,
        isCashIn: isCashIn,
        currency: currency,
      );
      return Right(voucher);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
