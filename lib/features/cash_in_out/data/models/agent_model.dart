import '../../domain/entities/agent.dart';

class AgentModel extends Agent {
  const AgentModel({
    required super.id,
    required super.agentName,
    required super.agentCode,
  });

  factory AgentModel.fromJson(Map<String, dynamic> json) {
    print('Parsing JSON: $json');
    return AgentModel(
      id: json['_id'] as String ?? '',
      agentName: json['agentName'] as String ?? '',
      agentCode: json['agentCode'] as String ?? '',
    );
  }
}
