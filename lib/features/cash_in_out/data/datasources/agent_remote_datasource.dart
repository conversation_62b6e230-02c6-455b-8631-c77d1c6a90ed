import '../models/agent_model.dart';
import '../models/agent_qr_response_model.dart';

abstract class AgentRemoteDataSource {
  /// Searches for an agent by agent ID
  /// Throws a [ServerException] for all error codes
  Future<AgentModel?> searchAgent(String agentId);

  /// Parses the agent QR code
  /// Throws a [ServerException] for all error codes
  Future<AgentQrResponseModel> parseAgentQr(String qrString);
}
