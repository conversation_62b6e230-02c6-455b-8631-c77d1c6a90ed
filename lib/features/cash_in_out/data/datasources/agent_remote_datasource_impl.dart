import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:flutter/foundation.dart';
import '../models/agent_model.dart';
import '../models/agent_qr_response_model.dart';
import 'agent_remote_datasource.dart';

class AgentRemoteDataSourceImpl implements AgentRemoteDataSource {
  final ApiService apiService;

  AgentRemoteDataSourceImpl({required this.apiService});

  @override
  Future<AgentModel?> searchAgent(String agentId) async {
    try {
      debugPrint('RemoteDataSource: Searching for agent with ID: $agentId');

      final result = await apiService.post(
        ApiEndpoints.agentsSearch,
        queryParameters: {'searchkey': agentId},
        parser: (data) {
          debugPrint('RemoteDataSource: Search response raw data: $data');

          if (data == null) {
            debugPrint('RemoteDataSource: API returned null data');
            return null;
          }

          if (data is! Map) {
            debugPrint(
                'RemoteDataSource: API returned non-map data: ${data.runtimeType}');
            return null;
          }

          if (data['success'] == true && data['data'] != null) {
            debugPrint(
                'RemoteDataSource: Found agent data, attempting to parse');
            try {
              final agent =
                  AgentModel.fromJson(data['data'] as Map<String, dynamic>);
              debugPrint(
                  'RemoteDataSource: Successfully parsed agent: ${agent.agentName}');
              return agent;
            } catch (e) {
              debugPrint('RemoteDataSource: Error parsing agent data: $e');
              return null;
            }
          }

          if (data['message'] != null) {
            debugPrint(
                'RemoteDataSource: API returned message: ${data['message']}');
          }

          debugPrint(
              'RemoteDataSource: API returned unsuccessful response or null data');
          return null;
        },
        requiresAuth: true, // Make sure authentication is required
      );

      if (result.isSuccess) {
        final agent = result.data;
        debugPrint(
            'RemoteDataSource: Search result: ${agent != null ? 'Agent found' : 'No agent found'}');
        return agent;
      } else {
        debugPrint(
            'RemoteDataSource: Search failed with error: ${result.error?.message}, status: ${result.error?.statusCode}');
        throw ServerException(
          message: result.error?.message ?? 'Unknown error',
          statusCode: result.error?.statusCode ?? 500,
        );
      }
    } catch (e) {
      debugPrint('RemoteDataSource error: $e');
      if (e is ServerException) rethrow;
      throw ServerException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  @override
  Future<AgentQrResponseModel> parseAgentQr(String qrString) async {
    try {
      debugPrint('RemoteDataSource: Parsing QR code...');

      final result = await apiService.post(
        ApiEndpoints.parseMerchantQr,
        data: {'qrString': qrString},
        parser: (data) {
          debugPrint('RemoteDataSource: Parse QR response: $data');
          if (data['success'] == true && data['data'] != null) {
            final merchantData = data['data'] as Map<String, dynamic>;
            return AgentQrResponseModel.fromJson(merchantData);
          }
          throw ServerException(
            message:
                data['message']?.toString() ?? 'Invalid response from server',
            statusCode: 400,
          );
        },
        requiresAuth: true,
      );

      if (result.isSuccess) {
        final merchant = result.data!;
        debugPrint(
            'RemoteDataSource: QR parsed successfully, userCode: ${merchant.userCode}');
        if (merchant.userCode != 'agent') {
          throw ServerException(
            message: 'Oops! This isn\'t an agent QR code.',
            statusCode: 400,
          );
        }
        return merchant;
      } else {
        debugPrint(
            'RemoteDataSource: Parse QR failed with error: ${result.error?.message}');
        throw ServerException(
          message: result.error?.message ?? 'Unknown error',
          statusCode: result.error?.statusCode ?? 500,
        );
      }
    } catch (e) {
      debugPrint('RemoteDataSource error: $e');
      if (e is ServerException) rethrow;
      throw ServerException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }
}
