import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_bloc.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_event.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_state.dart';
import 'package:cbrs/features/cash_in_out/domain/entities/agent.dart';
import 'package:cbrs/features/cash_in_out/domain/entities/bill.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class CashOutAgentAddAmountPage extends StatefulWidget {
  const CashOutAgentAddAmountPage({
    // required this.agentInfo,
    required this.id,
    required this.agentCode,
    required this.agentName,
    this.showAgentContainer = false,
    super.key,
  });
  // final Agent? agentInfo;
  final String id;
  final String agentName;
  final String agentCode;
  final bool showAgentContainer;
  @override
  State<CashOutAgentAddAmountPage> createState() =>
      _CashOutAgentAddAmountPageState();
}

class _CashOutAgentAddAmountPageState extends State<CashOutAgentAddAmountPage> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;
  bool _ignoreAmountCheck = true;
  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;
  String billRefNo = '';
  @override
  void initState() {
    super.initState();
    _currencyController = CurrencyInputController(
      currencyType: (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: 0,
      ignoreWalletAmountCheck: _ignoreAmountCheck,
      // TODOwidget.walletBalance,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.agentCashOut,
      pinController: _pinController,
      onPinSubmitted: handleSubmitPin,
      onTransactionSuccess: (response) {
        Navigator.pop(context);

        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  void handleSubmitPin(String pin) {
    context.read<TransactionBloc>().add(
          ConfirmTransferEvent(
            pin: pin,
            billRefNo: billRefNo,
            transactionType: tx_type.TransactionType.agentCashOut,
          ),
        );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }
      setState(() {
        _isLoading = true;
      });

      context.read<CashInCashOutBloc>().add(
            CheckTransferRulesRequested(
              amount: amount,
            ),
          );

      // context.read<WalletTransferBloc>().add(
      //       CheckWalletTransferRulesRequested(
      //         amount: amount,
      //         currency: GlobalVariable.currentlySelectedWallet ?? '',
      //         productType: 'cash_out',
      //       ),
      //     );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  void _showConfirmScreenBottomSheet(Bill response) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Agent Transfer',
        'Reciepent Name': widget?.agentName,
        'Reciepent Account': widget?.agentCode,
        'Amount': "${response.billAmount}ETB",
        'Service Charge': "${response.serviceCharge}ETB",
        'VAT': "${response.vat}ETB",
        'Date': AppMapper.safeFormattedDate(response.createdAt),
        // 'originalCurrency': response.currency,
        // 'createdAt': response.createdAt,
        // 'totalAmount': response.totalAmount,
        // 'billRefNo': response.billRefNo,
        // 'status': response.status,
      },
      confirmButtonText: 'Confirm Transfer',
      requiresOtp: response.authorizationType == 'PIN_AND_OTP',
      totalAmount: response.totalAmount ?? 0,
      billAmount: response.billAmount ?? 0,

      originalCurrency: response.currency ?? '',
      status: response.status ?? '',

      // WalletTransferAuthResponse.fromJson(response.toJson()).requiresOtp,
      billRefNo: response.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
        totalAmount: transaction.totalAmount ?? 0,
        billAmount: transaction.billAmount ?? 0,
        transactionId: transaction.id,
        billRefNo: transaction.billRefNo,
        originalCurrency: transaction.originalCurrency,
        status: 'Paid',
        title: 'Your Cash Out was completed successfully',
        {
          'Transaction Type': 'Agent Transfer',
          'Reciepent Name': widget?.agentName,
          'Reciepent Account': widget?.agentCode,
          'Amount': "${transaction.billAmount}ETB",
          'Service Charge': "${transaction.serviceCharge}ETB",
          'VAT': "${transaction.vat}ETB",
          'Date': AppMapper.safeFormattedDate(transaction.createdAt),
          'Transaction Ref.': transaction.billRefNo,

          // 'totalAmount': transaction.totalAmount,

          // 'originalCurrency': transaction.originalCurrency,

          //widget.memberInfo.phoneNumber,
          // 'billRefNo': transaction.billRefNo,
          // 'createdAt': transaction.createdAt.toString(),
          // 'serviceCharge': transaction.serviceCharge,
          // 'VAT': transaction.vat,
          // 'billAmount': transaction.billAmount,
          // 'status': transaction.status,
        });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WalletTransferBloc, WalletTransferState>(
      listenWhen: (previous, current) =>
          // current is CheckingWalletTransferRules ||
          current is WalletTransferRulesChecked ||
          current is WalletTransferLoading ||
          current is WalletTransferPinRequired ||
          current is WalletTransferFailure ||
          current is WalletTransferError,
      listener: (context, state) {
        if (
           
          // state is CheckingWalletTransferRules ||
            state is WalletTransferLoading) {
          setState(() => _isLoading = true);
        } else if (state is WalletTransferRulesChecked) {
          setState(() => _isLoading = false);
          debugPrint('state is WalletTransferRulesjjChecked');

          // Initiate wallet transfer after rules are checked
          /*
          context.read<WalletTransferBloc>().add(
                TransferToWalletEvent(
                  beneficiaryEmail: widget.isFromQuick
                      ? widget.recipent?.email
                      : widget.memberInfo?.email,
                  beneficiaryPhone: widget.isFromQuick
                      ? widget.recipent?.phone
                      : widget.memberInfo?.phoneNumber,
                  beneficiaryId: widget.isFromQuick
                      ? widget.recipent?.id
                      : widget.memberInfo?.id,
                  amount: _currencyController.numericAmount,
                  currency: GlobalVariable.currentlySelectedWallet ?? '',
                ),
              );
*/
        } else if (state is WalletTransferPinRequired) {
          debugPrint('state is WalletTransferPinRequired');
          setState(() {
            _isLoading = false;
          });
          // _showConfirmScreenBottomSheet(state.response.data);
        } else if (state is WalletTransferFailure ||
            state is WalletTransferError) {
          setState(() => _isLoading = false);
          CustomToastification(
            context,
            message: state is WalletTransferFailure
                ? state.message
                : (state as WalletTransferError).message,
          );
        }
      },
      buildWhen: (previous, current) =>
          current is WalletTransferInitial ||
          current is WalletTransferLoading ||
          current is WalletDetailsLoaded,
      builder: (context, state) {
        return BlocListener<CashInCashOutBloc, CashInCashOutState>(
          listener: (context, state) {
            if (state is CashInCashOutLoading) {
              setState(() {
                _isLoading = true;
              });
            } else {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is TransferRulesCheckSuccess) {
              context.read<CashInCashOutBloc>().add(
                    GenerateBillRequested(
                      agentId: widget?.id ?? '',
                      amount: _currencyController.numericAmount,
                      transactionType: 'cash_out',
                    ),
                  );
            } else if (state is CashInCashOutFailure) {
              CustomToastification(
                context,
                message: state.message,
              );
            }

            if (state is BillGenerationSuccess) {
              setState(() {
                billRefNo = state.bill.billRefNo;
              });
              _showConfirmScreenBottomSheet(state.bill);
            }
          },
          child: Scaffold(
            appBar: AppBar(
              title: const Text('Send Money'),
            ),
            body: SafeArea(
              bottom: false,
              child: BlocListener<TransactionBloc, TransactionState>(
                listenWhen: (previous, current) =>
                    current is ConfirmTransferSuccess ||
                    current is ConfirmTransferError,
                listener: (context, state) {
                  if (state is ConfirmTransferError) {
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                  }
                },
                child: BlocConsumer<HomeBloc, HomeState>(
                  listener: (context, state) {
                    if (state is HomeProfileLoadedState) {
                      setState(() {
                        _ignoreAmountCheck = false;
                        _currencyController = CurrencyInputController(
                          currencyType: CurrencyType.usd,
                          maxBalance: 0,
                          ignoreWalletAmountCheck: _ignoreAmountCheck,
                        );
                      });
                    }
                  },
                  builder: (context, state) {
                    // if (state is UserLoading)
                    //   return const CustomConnectLoader();
                    // if (state is UserLoaded) {
                    return widget.showAgentContainer
                        ? CurrencyInputWidget(
                            controller: _currencyController,
                            title: '',
                            transactionType: 'wallet_transfer',
                            subtitle: '',
                            header: Column(
                              children: [
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: const CustomPageHeader(
                                    pageTitle: 'Add Amount',
                                    description:
                                        'Enter the amount you wish to cash out, then submit to complete the transaction.',
                                  ),
                                ),
                                SizedBox(height: 12.h),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: RecipientCard(
                                    avatar: '_recipentAvatar',
                                    name: widget.agentName,
                                    accountNumber: widget.agentCode,
                                    isBirrTransfer: true,
                                    // recipientEmail: _emailController.text,
                                    onTap: () {},
                                  ),
                                ),
                              ],
                            ),
                            onContinue: _onContinuePressed,
                            isLoading: _isLoading,
                          )
                        : CurrencyInputWidget(
                            controller: _currencyController,
                            title: 'Add Amount',
                            transactionType: 'wallet_transfer',
                            subtitle:
                                'Enter the amount you wish to cash out, then submit to complete the transaction.',
                            onContinue: _onContinuePressed,
                            isLoading: _isLoading,
                          );
                    //   } else
                    //     return const SizedBox.shrink();
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
