import 'dart:io';

import 'package:cbrs/features/guest/application/bloc/guest_bloc.dart';
import 'package:cbrs/features/guest/presentation/views/guest_add_amount_screen.dart';
import 'package:cbrs/features/send_money/domain/entities/bank.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class GuestRecipientAccountNumberScreen extends StatefulWidget {
  const GuestRecipientAccountNumberScreen({
    required this.bank,
    super.key,
  });
  final Bank bank;

  @override
  State<GuestRecipientAccountNumberScreen> createState() =>
      _GuestRecipientAccountNumberScreenState();
}

class _GuestRecipientAccountNumberScreenState
    extends State<GuestRecipientAccountNumberScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountNumberController = TextEditingController();
  final _senderNameController = TextEditingController();
  final _reasonController = TextEditingController();
  bool _isValidAccount = false;
  final bool _isLoading = false;
  bool _showAccountPreview = false;
  Map<String, dynamic>? _accountData;

  bool _accountFieldTouched = false;
  bool _nameFieldTouched = false;
  bool _isLookupMode = true;
  bool _isLookupLoading = false;

  @override
  void initState() {
    super.initState();
    _accountNumberController.addListener(_validateForm);
    _senderNameController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _accountNumberController.dispose();
    _senderNameController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _isValidAccount = _accountNumberController.text.length >= 8 &&
          _senderNameController.text.isNotEmpty;

      // Reset lookup mode if account number changes or no recipient preview
      if (!_showAccountPreview || _accountData == null) {
        _isLookupMode = true;
      }
    });
  }

  Future<void> _lookupAccount(String accountNumber) async {
    if (accountNumber.length >= 8) {
      context.read<GuestBloc>().add(
            GuestAccountLookupEvent(
              accountNumber: accountNumber,
              bankCode: widget.bank.etSwitchCode,
            ),
          );
    } else {
      setState(() {
        _showAccountPreview = false;
        _accountData = null;
      });
    }
  }

  void _handleContinue() {
    FocusScope.of(context).unfocus(); // Hide the keyboard

    // context.pushNamed(
    //   AppRouteName.guestAddAmountScreen,
    //   extra: {
    //     'recipientAccountNumber': _accountNumberController.text,
    //     'senderName': _senderNameController.text,
    //     'recipientName': 'heellsos',
    //     //_accountData!['customerName'] as String,
    //     'reason': '_reasonController.text.isEmpty',
    //     'bank': widget.bank,
    //   },
    // );

    // return;
    setState(() {
      _accountFieldTouched = true;
      _nameFieldTouched = true;
    });

    if (_isLookupMode) {
      // Perform account lookup
      if (_formKey.currentState!.validate()) {
        setState(() {
          _isLookupLoading = true;
        });
        _lookupAccount(_accountNumberController.text);
      }
    } else {
      // Only navigate if we have account data
      if (_formKey.currentState!.validate() && _accountData != null) {
        final reason =
            _reasonController.text.isEmpty ? null : _reasonController.text;

        context.pushNamed(
          AppRouteName.guestAddAmountScreen,
          extra: {
            'recipientAccountNumber': _accountNumberController.text,
            'senderName': _senderNameController.text,
            'recipientName': _accountData!['customerName'] as String,
            'reason': reason,
            'bank': widget.bank,
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = Theme.of(context).primaryColor;

    return BlocConsumer<GuestBloc, GuestState>(
      listener: (context, state) {
        if (state is AccountLookupSuccess) {
          setState(() {
            _accountData = {
              'customerName': state.accountDetails.customerName,
              'accountNumber': state.accountDetails.accountNumber,
            };
            _showAccountPreview = true;
            _isLookupLoading = false;
            _isLookupMode = false;
          });
        } else if (state is GuestError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.message,
                style: GoogleFonts.outfit(),
              ),
              behavior: SnackBarBehavior.floating,
            ),
          );
          setState(() {
            _showAccountPreview = false;
            _accountData = null;
            _isLookupLoading = false;
            _isLookupMode = true;
          });
        }
      },
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            title: Text(
              'Bank Transfer',
              style: GoogleFonts.outfit(
                fontWeight: FontWeight.w500,
                fontSize: 18.sp,
              ),
            ),
            leading: IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(Icons.arrow_back),
            ),
          ),
          body: SafeArea(
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: GestureDetector(
                        onTap: () {
                          FocusScope.of(context).unfocus(); // Hide the keyboard
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 22.h,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Send Money',
                                style: GoogleFonts.outfit(
                                  fontSize: 24..sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Text(
                                "Enter the recipient's account number for ${widget.bank.name} and proceed to send the money.",
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  color: const Color(0xFFAAAAAA),
                                ),
                              ),
                              SizedBox(height: 24.h),
                              Text(
                                'Sender Name',
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  color: const Color(0xFFAAAAAA),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Focus(
                                onFocusChange: (hasFocus) {
                                  if (!hasFocus) {
                                    setState(() {
                                      _nameFieldTouched = true;
                                    });
                                    _validateForm();
                                  }
                                },
                                child: CustomTextFormField(
                                  controller: _senderNameController,
                                  hintText: 'Enter Sender Name',
                                  validator: (value) {
                                    if (!_nameFieldTouched) return null;
                                    if (value == null || value.isEmpty) {
                                      return 'Sender name is required';
                                    }
                                    return null;
                                  },
                                  onEditingComplete: () {
                                    _formKey.currentState?.validate();
                                  },
                                  style: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    color: Colors.black87,
                                  ),
                                  hintStyle: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    color: Colors.grey[400],
                                  ),
                                  borderRadius: 12.r,
                                ),
                              ),
                              SizedBox(height: 24.h),
                              Text(
                                'Account Number',
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  color: const Color(0xFFAAAAAA),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Focus(
                                onFocusChange: (hasFocus) {
                                  if (!hasFocus) {
                                    setState(() {
                                      _accountFieldTouched = true;
                                    });
                                    _validateForm();
                                  }
                                },
                                child: CustomTextFormField(
                                  controller: _accountNumberController,
                                  textInputType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(15),
                                  ],
                                  validator: (value) {
                                    if (!_accountFieldTouched) return null;
                                    if (value == null || value.isEmpty) {
                                      return 'Account number is required';
                                    }
                                    if (value.length < 8) {
                                      return 'Account number must be at least 8 digits';
                                    }
                                    return null;
                                  },
                                  onEditingComplete: () {
                                    _formKey.currentState?.validate();
                                  },
                                  onChange: (value) {
                                    setState(() {
                                      _showAccountPreview = false;
                                      _accountData = null;
                                      _isValidAccount = value.length >= 3;
                                    });
                                  },
                                  hintText: 'Enter Account Number',
                                  style: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    color: Colors.black87,
                                  ),
                                  hintStyle: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    color: Colors.grey[400],
                                  ),
                                  borderRadius: 12.r,
                                ),
                              ),
                              if (state is GuestLoading)
                                Padding(
                                  padding: EdgeInsets.only(top: 16.h),
                                  child: _buildLoadingPreview(),
                                ),
                              if (_showAccountPreview && _accountData != null)
                                Padding(
                                  padding: EdgeInsets.only(top: 16.h),
                                  child: _buildAccountPreview(),
                                ),
                              SizedBox(height: 24.h),
                              Text(
                                'Reason (Optional)',
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  color: const Color(0xFFAAAAAA),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              CustomTextFormField(
                                controller: _reasonController,
                                hintText: 'Enter Reason (Optional)',
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  color: Colors.black87,
                                ),
                                hintStyle: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  color: Colors.grey[400],
                                ),
                                borderRadius: 12.r,
                                maxLines: 1,
                                overrideValidator: true,
                              ),
                              SizedBox(height: 24.h),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.fromLTRB(
                      16.w,
                      0,
                      16.w,
                      Platform.isIOS ? 24 : 16,
                    ),
                    child: CustomButton(
                      text: (_showAccountPreview && _accountData != null)
                          ? 'Continue'
                          : 'Continue',
                      onPressed: _isValidAccount ? _handleContinue : null,
                      showLoadingIndicator: _isLookupLoading &&
                          !(_showAccountPreview && _accountData != null),
                      options: CustomButtonOptions(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        color: _isValidAccount ? themeColor : Colors.grey[200],
                        textStyle: GoogleFonts.outfit(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color:
                              _isValidAccount ? Colors.white : Colors.grey[400],
                        ),
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAccountPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Theme.of(context).primaryColor,
            radius: 20.r,
            child: Text(
              (_accountData!['customerName'] as String)
                  .substring(0, 1)
                  .toUpperCase(),
              style: GoogleFonts.outfit(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16.sp,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _accountData!['customerName'] as String,
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _accountData!['accountNumber'] as String,
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: const Color(0xff065234),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).scale(
          begin: const Offset(0.95, 0.95),
          curve: Curves.easeOutExpo,
        );
  }

  Widget _buildLoadingPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[200]!,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
            ),
          )
              .animate(
                onPlay: (controller) => controller.repeat(),
              )
              .shimmer(
                duration: 1200.ms,
                color: Colors.grey[300],
              ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 120,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                )
                    .animate(
                      onPlay: (controller) => controller.repeat(),
                    )
                    .shimmer(
                      duration: 1200.ms,
                      color: Colors.grey[300],
                    ),
                const SizedBox(height: 4),
                Container(
                  width: 80,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                )
                    .animate(
                      onPlay: (controller) => controller.repeat(),
                    )
                    .shimmer(
                      duration: 1200.ms,
                      color: Colors.grey[300],
                    ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
