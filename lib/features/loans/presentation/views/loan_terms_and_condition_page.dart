import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_event.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_state.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_terms_and_conditions_container.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';

class LoanTermsAndConditionsPage extends StatefulWidget {
  const LoanTermsAndConditionsPage({
    required this.bank,
    required this.loanItem,
    required this.selectedUpfrontPayment,
    super.key,
  });

  final LoanBank bank;
  final LoanItem loanItem;
  final String selectedUpfrontPayment;

  @override
  State<LoanTermsAndConditionsPage> createState() =>
      _LoanTermsAndConditionsPageState();
}

class _LoanTermsAndConditionsPageState
    extends State<LoanTermsAndConditionsPage> {
  @override
  void initState() {
    super.initState();
    _fetchLoanTerms();
  }

  void _fetchLoanTerms() {
    debugPrint(
      '🔍 Loan terms will be fetched by container for bank: ${widget.bank.id}',
    );
  }

  String appliedLoanPeriod = '';
  bool isLoanTermsFetched = false;
  bool _isBottomSheetShowing = false;
  bool _hasApplied = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Loan Terms & Conditions',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () =>
              _handleBackButton().then((_) => Navigator.pop(context)),
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<LoanItemsBloc, LoanState>(
            listener: (context, state) {
              if (state is LoanApplicationSuccess) {
                _handleLoanApplicationSuccess(state.application);
              } else if (state is ApplicationTransactionGenerated) {
                _handleApplicationTransactionGenerated(state.transaction);
              } else if (state is LoanPaymentConfirmed) {
                _handleLoanPaymentConfirmed(state.confirmation);
              } else if (state is LoanError ||
                  state is ApplicationTransactionError ||
                  state is LoanPaymentConfirmationError) {
                // Reset flags on any error to allow retry
                setState(() {
                  _isBottomSheetShowing = false;
                  _hasApplied = false;
                });

                final message = state is LoanError
                    ? state.message
                    : state is ApplicationTransactionError
                        ? state.message
                        : (state as LoanPaymentConfirmationError).message;
                CustomToastification(
                  context,
                  message: message,
                );
              }
            },
          ),
          BlocListener<LoanTermsCubit, LoanTermsState>(
            listener: (context, state) {
              if (state is LoanTermsLoaded) {
                setState(() {
                  isLoanTermsFetched = true;
                });
              } else if (state is LoanTermsError) {
                setState(() {
                  isLoanTermsFetched = false;
                });
              }
            },
          ),
        ],
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const CustomPageHeader(
                        pageTitle: 'Our Terms & Condition',
                        description:
                            'Read our terms and conditions carefully before your '
                            'continue with your application.',
                      ),
                      SizedBox(height: 16.h),
                      _buildBankCard(context),
                      SizedBox(height: 12.h),
                      Expanded(
                        child: LoanTermsAndConditionsContainer(
                          bankId: widget.bank.id,
                          loanType: widget.loanItem.type == LoanItemType.car
                              ? 'car'
                              : 'mortgage',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              BlocConsumer<LoanItemsBloc, LoanState>(
                listener: (context, state) {
                  if (state is LoanError) {
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                  }
                },
                builder: (context, state) {
                  final isLoading = state is LoanApplicationLoading;
                  final isProcessing = isLoading || _isBottomSheetShowing;
                  final canApply =
                      isLoanTermsFetched && !isProcessing && !_hasApplied;

                  String buttonText;
                  if (_hasApplied) {
                    buttonText = 'Application Submitted';
                  } else if (isProcessing) {
                    buttonText = 'Processing...';
                  } else {
                    buttonText = 'Apply Now';
                  }

                  return CustomPagePadding(
                    bottom: 16.h,
                    child: SafeArea(
                      child: CustomRoundedBtn(
                        btnText: buttonText,
                        onTap: canApply ? _handleApplyNow : null,
                        isLoading: isProcessing,
                        isBtnActive: canApply,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _handleBackButton() async {
    // If bottom sheet is showing or application is in progress, close it and reset state
    if (_isBottomSheetShowing || _hasApplied) {
      setState(() {
        _isBottomSheetShowing = false;
        _hasApplied = false;
      });

      // If there's a modal bottom sheet open, close it
      if (_isBottomSheetShowing && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    }

    return true;
  }

  void _handleApplyNow() {
    setState(() {
      _hasApplied = true;
    });

    final loanBloc = BlocProvider.of<LoanItemsBloc>(context);

    final upfrontPaymentPercentage = widget.selectedUpfrontPayment;

    loanBloc.add(
      ApplyLoanEvent(
        loanId: widget.bank.loans.first.id,
        productId: widget.loanItem.id,
        upfrontPaymentPercentage: upfrontPaymentPercentage,
        loanType: widget.loanItem.type,
      ),
    );
  }

  void _handleLoanApplicationSuccess(LoanApplication application) {
    final loanBloc = BlocProvider.of<LoanItemsBloc>(context);

    debugPrint('Generating application transaction for ID: ${application.id}');

    loanBloc.add(
      GenerateApplicationTransactionEvent(
        loanApplicationId: application.id,
        loanType: widget.loanItem.type,
      ),
    );
  }

  void _handleApplicationTransactionGenerated(dynamic transaction) {
    _showPaymentConfirmationBottomSheet(transaction);
  }

  void _handleLoanPaymentConfirmed(dynamic confirmation) {
    // Reset only the bottom sheet flag, keep _hasApplied as true
    // since payment was successful
    setState(() {
      _isBottomSheetShowing = false;
    });

    // Show success bottom sheet with loan payment details
    _showLoanPaymentSuccessBottomSheet(confirmation);
  }

  void _showPaymentConfirmationBottomSheet(dynamic transaction) {
    // Prevent multiple bottom sheets from being shown
    if (_isBottomSheetShowing) {
      debugPrint('⚠️ Bottom sheet already showing, ignoring request');
      return;
    }

    setState(() {
      _isBottomSheetShowing = true;
    });

    // Extract transaction details
    final billRefNo = transaction.billRefNo as String;
    final amount = transaction.billAmount.toString();

    final itemPrice = widget.loanItem.type == LoanItemType.car
        ? widget.loanItem.price
        : widget.loanItem.price;
    final upfrontPercentage =
        double.tryParse(widget.selectedUpfrontPayment) ?? 0;
    final upfrontAmount = itemPrice * (upfrontPercentage / 100);
    final loanAmount = itemPrice - upfrontAmount;
    final loanPeriod = widget.bank.loans.first.loanPeriod;

    // Create transaction bottom sheets manager
    final pinController = TextEditingController();
    final bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: TransactionType.loanRepayment,
      pinController: pinController,
      onPinSubmitted: (pin) => _confirmLoanPayment(
        transactionType: 'application_fee',
        billRefNo: billRefNo,
        pin: pin,
      ),
      onTransactionSuccess: (response) {},
      onTransactionComplete: () {
        // Reset the bottom sheet flag when transaction is complete
        if (mounted) {
          setState(() {
            _isBottomSheetShowing = false;
          });
        }
      },
    );

    // Use a custom modal bottom sheet to allow proper dismissal handling
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(bottomSheetContext).viewInsets.bottom,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(bottomSheetContext).size.height * 0.9,
            ),
            child: CustomConfirmTransactionBottomSheet(
              data: {
                'Transaction Type': 'Loan Application Fee',
                'Loan Type': widget.loanItem.type == LoanItemType.car
                    ? 'Car Loan'
                    : 'Mortgage Loan',
                'Product Name': widget.loanItem.name,
                'Bank': widget.bank.name,
                'Upfront Payment': '${widget.selectedUpfrontPayment}%',
                'Application Fee': '$amount USD',
                'Bill Reference': billRefNo,
                'Status': 'Pending Payment',
              },
              transactionType: TransactionType.loanRepayment.value,
              confirmButtonText: 'Confirm Payment',
              billAmount: (transaction.billAmount as num).toDouble(),
              totalAmount: (transaction.billAmount as num).toDouble(),
              originalCurrency: 'USD',
              onContinue: () {
                // Close the bottom sheet
                Navigator.pop(bottomSheetContext);

                bottomSheetsManager.showConfirmPinScreenBottomSheet(
                  billRefNo: billRefNo,
                );
              },
            ),
          ),
        );
      },
    ).then((_) {
      // Reset state when bottom sheet is dismissed
      if (mounted) {
        setState(() {
          _isBottomSheetShowing = false;

          final currentState = context.read<LoanItemsBloc>().state;
          if (currentState is! LoanPaymentConfirmed) {
            _hasApplied = false;
            debugPrint(
              '🔄 Payment confirmation bottom sheet dismissed, application canceled',
            );
          }
        });
      }
    });
  }

  void _confirmLoanPayment({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) {
    context.read<LoanItemsBloc>().add(
          ConfirmLoanPaymentEvent(
            transactionType: transactionType,
            billRefNo: billRefNo,
            pin: pin,
          ),
        );
  }

  void _showLoanPaymentSuccessBottomSheet(dynamic confirmation) {
    // Create a transaction bottom sheets manager for showing success
    final pinController = TextEditingController();
    final bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: TransactionType.loanRepayment,
      pinController: pinController,
      onPinSubmitted: (pin) {},
      onTransactionSuccess: (response) {},
      onTransactionComplete: () {
        if (mounted) {
          setState(() {
            _isBottomSheetShowing = false;
          });
        }
      },
    );

    // Extract loan payment confirmation details
    final applicationFeeTransaction = confirmation.applicationFeeTransaction;
    final loanAmount = confirmation.loanAmount ?? '0';
    final monthlyPayment = confirmation.monthlyPayment ?? '0';
    final totalPayment = confirmation.totalPayment ?? '0';
    final loanApplicationCode = confirmation.loanApplicationCode ?? '';

    // Show success bottom sheet with loan payment details
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(bottomSheetContext).viewInsets.bottom,
          ),
          child: CustomSuccessTransactionBottomSheet(
            data: {
              'Transaction Type': 'Loan Application Fee Payment',
              'Loan Application Code': loanApplicationCode,
              'Loan Amount': '$loanAmount USD',
              'Monthly Payment': '$monthlyPayment USD',
              'Total Payment': '$totalPayment USD',
              'Application Fee':
                  '${applicationFeeTransaction?.billAmount ?? 0} USD',
              'Payment Method':
                  applicationFeeTransaction?.paymentMethod ?? 'WALLET',
              'Transaction Status': 'COMPLETED',
              'Bill Reference': applicationFeeTransaction?.billRefNo ?? '',
              'Date': AppMapper.safeFormattedDate(
                applicationFeeTransaction?.paidDate ?? DateTime.now(),
              ),
            },
            isFromLoan: true,
            totalAmount:
                (applicationFeeTransaction?.billAmount as num? ?? 0).toDouble(),
            billAmount:
                (applicationFeeTransaction?.billAmount as num? ?? 0).toDouble(),
            originalCurrency: 'USD',
            transactionId:
                (applicationFeeTransaction?.billRefNo ?? '') as String,
            billRefNo: (applicationFeeTransaction?.billRefNo ?? '') as String,
            title:
                'Your loan application fee payment was completed successfully',
            buttonText: 'Back to Home',
            onContinue: () {
              Navigator.pop(bottomSheetContext);
            },
          ),
        );
      },
    );
  }

  Widget _buildBankCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Image.network(
            widget.bank.logo ?? '',
            width: 32.w,
            height: 32.h,
            errorBuilder: (context, error, stackTrace) =>
                Icon(Icons.account_balance, size: 32.w),
          ),
          SizedBox(width: 12.w),
          Text(
            widget.bank.name,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
