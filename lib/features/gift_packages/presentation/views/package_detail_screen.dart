import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:cbrs/core/utils/to_title_case.dart';
import 'package:cbrs/features/gift_packages/domain/entities/package_detail.dart';
import 'package:cbrs/features/gift_packages/domain/entities/package_item.dart';
import 'package:cbrs/features/gift_packages/domain/repositories/gift_package_repository.dart';
import 'package:cbrs/features/gift_packages/presentation/widgets/package_detail_shimmer.dart';
import 'package:cbrs/features/gift_packages/presentation/widgets/thumbnail_shimmer.dart';
import 'package:cbrs/features/guest/presentation/widget/show_guest_mode_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class PackageDetailScreen extends StatefulWidget {
  final String packageId;
  final bool? isGuest;

  const PackageDetailScreen({
    required this.packageId,
    this.isGuest = false,
    Key? key,
  }) : super(key: key);

  @override
  State<PackageDetailScreen> createState() => _PackageDetailScreenState();
}

class _PackageDetailScreenState extends State<PackageDetailScreen> {
  int quantity = 1;
  int currentImageIndex = 0;
  late Future<PackageDetail> _packageDetailFuture;
  bool _toastShown = false;

  @override
  void initState() {
    super.initState();
    _packageDetailFuture =
        context.read<GiftPackageRepository>().getPackageDetail(widget.packageId)
          ..then((detail) {}).catchError((error) {
            print('❌ Error fetching package detail: $error');
          });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'Gift Package',
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
                fontSize: 18,
              ),
        ),
      ),
      body: FutureBuilder<PackageDetail>(
        future: _packageDetailFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const PackageDetailShimmer();
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${snapshot.error}'),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _packageDetailFuture = context
                            .read<GiftPackageRepository>()
                            .getPackageDetail(widget.packageId);
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final package = snapshot.data!;

          final images = [
            if (package.featureImage.isNotEmpty) package.featureImage,
            ...package.galleryImage.where((image) => image.isNotEmpty),
          ];

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main Image with Navigation
                Stack(
                  alignment: Alignment.center,
                  children: [
                    LayoutBuilder(
                      builder: (context, constraints) {
                        final screenWidth = MediaQuery.of(context).size.width;
                        final screenHeight = MediaQuery.of(context).size.height;
                        // Calculate responsive height (35% of screen height, min 250px, max 400px)
                        final imageHeight =
                            (screenHeight * 0.35).clamp(250.0, 400.0);

                        return SizedBox(
                          height: imageHeight,
                          width: screenWidth,
                          child: images.isEmpty
                              ? const Center(
                                  child: Icon(Icons.image_not_supported))
                              : CachedNetworkImage(
                                  imageUrl: images[currentImageIndex],
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      const Center(
                                    child: Icon(Icons.error),
                                  ),
                                ),
                        );
                      },
                    ),
                    // Discount badge
                    if (package.discountBannerLevel.isNotEmpty) ...[
                      if (package.discountBannerLevel.first.discountType ==
                          'Percentage')
                        Positioned(
                          top: MediaQuery.of(context).size.height *
                              0.02, // 2% from top
                          left: MediaQuery.of(context).size.width *
                              0.02, // 2% from left
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal:
                                  MediaQuery.of(context).size.width * 0.02,
                              vertical:
                                  MediaQuery.of(context).size.height * 0.005,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Text(
                              '${package.discountBannerLevel.first.discountPercentage?.toStringAsFixed(0)}% OFF',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width *
                                    0.035, // Responsive font size
                              ),
                            ),
                          ),
                        ),
                    ] else if (package.discountPackageLevel.isNotEmpty) ...[
                      // Similar updates for package level discount badge
                      if (package.discountPackageLevel.first.discountType ==
                          'Percentage')
                        Positioned(
                          top: MediaQuery.of(context).size.height * 0.02,
                          left: MediaQuery.of(context).size.width * 0.02,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal:
                                  MediaQuery.of(context).size.width * 0.02,
                              vertical:
                                  MediaQuery.of(context).size.height * 0.005,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Text(
                              '${package.discountPackageLevel.first.discountPercentage?.toStringAsFixed(0)}% OFF',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize:
                                    MediaQuery.of(context).size.width * 0.035,
                              ),
                            ),
                          ),
                        ),
                    ],
                    // Navigation buttons
                    if (images.length > 1) ...[
                      _buildNavigationButton(
                        icon: Icons.chevron_left,
                        onPressed: () {
                          if (currentImageIndex > 0) {
                            setState(() => currentImageIndex--);
                          }
                        },
                      ),
                      _buildNavigationButton(
                        icon: Icons.chevron_right,
                        onPressed: () {
                          if (currentImageIndex < images.length - 1) {
                            setState(() => currentImageIndex++);
                          }
                        },
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 16),

                // Thumbnail Images
                if (images.length > 1)
                  Container(
                    height: 90,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: images.length,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemBuilder: (context, index) => _buildThumbnail(
                        images[index],
                        index == currentImageIndex,
                        () => setState(() => currentImageIndex = index),
                      ),
                    ),
                  ),

                // Package Details
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        package.name,
                        style:
                            Theme.of(context).textTheme.titleMedium!.copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                      ),
                      const SizedBox(height: 8),
                      _buildPriceAndQuantity(package),
                      const SizedBox(height: 24),
                      _buildDescription(package),
                      const SizedBox(height: 24),
                      _buildMerchantInfoContainer(package),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: FutureBuilder<PackageDetail>(
        future: _packageDetailFuture,
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const SizedBox.shrink();
          return _buildBottomBar(snapshot.data!);
        },
      ),
    );
  }

  Widget _buildMerchantInfoContainer(PackageDetail package) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Merchant Info',
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: const DecorationImage(
              image: AssetImage(MediaRes.birrRecipentBgColor),
              fit: BoxFit.fill,
            ),
          ),
          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(10),
          //   color: Colors.white,
          //   boxShadow: [
          //     BoxShadow(
          //       color: Colors.black.withValues(alpha: 0.1),
          //       blurRadius: 2,
          //       offset: const Offset(0, 1),
          //     ),
          //   ],
          // ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final maxWidth = constraints.maxWidth;
              final logoSize =
                  (maxWidth * 0.2).clamp(60.0, 80.0); // 20% of width, clamped
              final fontSize =
                  (maxWidth * 0.04).clamp(14.0, 18.0); // Responsive font size

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(width: maxWidth * 0.03), // 3% spacing
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          toTitleCase(package.merchant.merchantName),
                          style: TextStyle(
                            fontSize:
                                fontSize * 1.2, // 20% larger than base fontSize
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              package.merchant.phoneNumber,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w400,
                                fontSize:
                                    fontSize * 0.9, // 90% of base fontSize
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: maxWidth * 0.02), // 2% spacing
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonSize = (screenWidth * 0.1)
        .clamp(36.0, 48.0); // 10% of screen width, clamped between 36 and 48
    final iconSize = (screenWidth * 0.06)
        .clamp(20.0, 32.0); // 6% of screen width, clamped between 20 and 32

    return Positioned(
      left: icon == Icons.chevron_left
          ? screenWidth * 0.03
          : null, // 3% from edge
      right: icon == Icons.chevron_right ? screenWidth * 0.03 : null,
      child: CircleAvatar(
        radius: buttonSize / 2,
        backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
        child: IconButton(
          icon:
              Icon(icon, color: Theme.of(context).primaryColor, size: iconSize),
          onPressed: onPressed,
          padding: EdgeInsets.zero,
          constraints: BoxConstraints(
            minWidth: buttonSize,
            minHeight: buttonSize,
          ),
        ),
      ),
    );
  }

  Widget _buildThumbnail(String imageUrl, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.transparent,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            placeholder: (context, url) => const ThumbnailShimmer(),
            errorWidget: (context, url, error) => Container(
              width: 60,
              height: 60,
              color: Colors.grey[300],
              child: const Icon(Icons.error),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceAndQuantity(PackageDetail package) {
    final theme = Theme.of(context);
    final discountedPrice = _calculateDiscountedPrice(package);
    final hasDiscount = package.discountBannerLevel.isNotEmpty ||
        package.discountPackageLevel.isNotEmpty;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hasDiscount) ...[
              Text(
                PriceFormatter.formatPrice(discountedPrice),
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontSize: 24,
                      fontWeight: FontWeight.w800,
                    ),
              ),
              Text(
                PriceFormatter.formatPrice(package.unitPrice.toString()),
                style: const TextStyle(
                  fontSize: 16,
                  decoration: TextDecoration.lineThrough,
                  color: Colors.grey,
                ),
              ),
            ] else ...[
              Text(
                PriceFormatter.formatPrice(package.unitPrice.toString()),
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontSize: 24,
                      fontWeight: FontWeight.w800,
                    ),
              ),
            ],
          ],
        ),
        Container(
          width: 120,
          height: 45,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildQuantityButton(
                Icons.remove,
                () {
                  if (quantity > 1) {
                    setState(() {
                      quantity--;
                      _toastShown = false;
                    });
                  }
                },
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              _buildQuantityButton(
                Icons.add,
                () {
                  if (quantity < 15) {
                    setState(() {
                      quantity++;
                      _toastShown = false;
                    });
                  } else if (!_toastShown) {
                    _toastShown = true;
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: CustomToast(
                          message:
                              'You can only purchase up to 15 items per order. Please adjust your quantity.',
                          isError: true,
                        ),
                        behavior: SnackBarBehavior.floating,
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityButton(IconData icon, VoidCallback onPressed) {
    return Container(
      child: IconButton(
        icon: Icon(icon),
        onPressed: onPressed,
        color: Colors.white,
        // padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 20,
          minHeight: 20,
        ),
      ),
    );
  }

  Widget _buildDescription(PackageDetail package) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Description',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          package.description,
          style: TextStyle(
            fontSize: 14,
            color: Colors.black,
            fontWeight: FontWeight.w400,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar(PackageDetail package) {
    final theme = Theme.of(context);

    final hasDiscount = package.discountBannerLevel.isNotEmpty ||
        package.discountPackageLevel.isNotEmpty;

    // Use the same calculation as _buildPriceAndQuantity
    final discountedPrice = _calculateDiscountedPrice(package);
    final totalPrice =
        (double.parse(discountedPrice) * quantity).toStringAsFixed(2);
    return SafeArea(
      child: Container(
        margin: EdgeInsets.only(bottom: 12),
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Container(
          padding: const EdgeInsets.all(6),
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                child: CircleAvatar(
                  radius: 24,
                  backgroundImage:
                      CachedNetworkImageProvider(package.featureImage),
                ),
              ),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      package.name,
                      style: GoogleFonts.outfit(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      PriceFormatter.formatPrice(totalPrice),
                      style: GoogleFonts.outfit(
                        fontSize: 14,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              CustomButton(
                text: 'Buy',
                onPressed: () {
                  if (widget.isGuest == true) {
                    showGuestModeBottomSheet(context);
                    return;
                  } else {
                    _proceedToPurchase(package);
                  }
                },
                options: CustomButtonOptions(
                  width: 100,
                  height: 48,
                  color: Colors.white.withOpacity(0.12),
                  textStyle: GoogleFonts.outfit(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  borderRadius: BorderRadius.circular(32),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _calculateDiscountedPrice(PackageDetail package) {
    // Prioritize banner level discount
    if (package.discountBannerLevel.isNotEmpty) {
      final bannerDiscount = package.discountBannerLevel.first;
      if (bannerDiscount.discountType == 'Percentage') {
        return (package.unitPrice *
                (1 - bannerDiscount.discountPercentage! / 100))
            .toStringAsFixed(2);
      } else if (bannerDiscount.discountType == 'Fixed') {
        return (package.unitPrice - bannerDiscount.discountAmount!)
            .toStringAsFixed(2);
      }
    }

    // Then check package level discount
    if (package.discountPackageLevel.isNotEmpty) {
      final packageDiscount = package.discountPackageLevel.first;
      if (packageDiscount.discountType == 'Percentage') {
        return (package.unitPrice *
                (1 - packageDiscount.discountPercentage! / 100))
            .toStringAsFixed(2);
      } else if (packageDiscount.discountType == 'Fixed') {
        return (package.unitPrice - packageDiscount.discountAmount!)
            .toStringAsFixed(2);
      }
    }

    return package.unitPrice.toStringAsFixed(2);
  }

  double _getEffectiveDiscount(PackageDetail package) {
    // Always prioritize banner level discount if it exists
    if (package.discountBannerLevel.isNotEmpty) {
      final bannerDiscount = package.discountBannerLevel.first;
      if (bannerDiscount.discountType == 'Percentage') {
        return bannerDiscount.discountPercentage ?? 0;
      }
    }

    // Only use package level discount if no banner level discount exists
    if (package.discountPackageLevel.isNotEmpty) {
      final packageDiscount = package.discountPackageLevel.first;
      if (packageDiscount.discountType == 'Percentage') {
        return packageDiscount.discountPercentage ?? 0;
      }
    }

    return 0;
  }

  void _proceedToPurchase(PackageDetail package) {
    final packageItem = PackageItem(
      id: package.id,
      name: package.name,
      description: package.description,
      featureImage: package.featureImage,
      galleryImage: package.galleryImage,
      unitPrice: package.unitPrice,
      category: package.category,
      merchant: package.merchant
          .merchantName, // This should still work as we're just using the name
      isDeleted: package.isDeleted,
      createdAt: package.createdAt,
      updatedAt: package.updatedAt,
      discountPackageLevel: package.discountPackageLevel,
      discountBannerLevel: package.discountBannerLevel,
    );

    String sellingPrice = _calculateDiscountedPrice(package);
    context.pushNamed(
      AppRouteName.giftRecipientDetails,
      extra: {
        'item': packageItem,
        'quantity': quantity,
        'sellingPrice': sellingPrice,
      },
    );
  }

  // Helper widgets implementation...
}
