import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class MoneyRequestCheckAmountScreen extends StatefulWidget {
  const MoneyRequestCheckAmountScreen({required this.moneyRequest, super.key});

  final MoneyRequestEntity moneyRequest;

  @override
  State<MoneyRequestCheckAmountScreen> createState() =>
      _MoneyRequestCheckAmountScreenState();
}

class _MoneyRequestCheckAmountScreenState
    extends State<MoneyRequestCheckAmountScreen> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;

  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;

  String billRefNo = '';
  @override
  void initState() {
    super.initState();

    context.read<MoneyRequestDetailBloc>().add(
          const GetWalletDetailEvent(),
        );

    _currencyController = CurrencyInputController(
      currencyType: widget.moneyRequest.currency == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: 0,
      defaultAmount: widget.moneyRequest.billAmount,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.moneyRequest,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        debugPrint(
          'piin $pin billref bo  $billRefNo  transactionType ${tx_type.TransactionType.moneyRequest}',
        );

        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: billRefNo,
                transactionType: tx_type.TransactionType.moneyRequest,
              ),
            );
        // Accept the money request with PIN
        // context.read<MoneyRequestDetailBloc>().add(
        //       AcceptRequestActionEvent(
        //         transactionID: widget.moneyRequest.transactionId,
        //         amount: widget.moneyRequest.billAmount,
        //       ),
        //     );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      _checkTransferRule();
   
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  Future<void> _checkTransferRule() async {
    final amount = _currencyController.numericAmount;

    context.read<CheckRuleTransferBloc>().add(
          CheckWalletTransferRulesRequested(
            amount: 19999,
            currency: _currencyController.currencyType == CurrencyType.usd
                ? 'USD'
                : 'ETB',
            productType: 'wallet_transfer',
          ),
        );
  }

  Future<void> _handleCustomAmount(double amount) async {
    context.read<MoneyRequestDetailBloc>().add(
          AcceptRequestActionEvent(
            transactionID: widget.moneyRequest.transactionId,
            amount: amount,
          ),
        );
  }

  void _showConfirmScreenBottomSheet(MoneyRequestEntity response) {
    setState(() {
      billRefNo = response.billRefNo;
    });
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Money Request Payment',
        'Sender Name': response.senderName,
        'Sender Account': response.senderEmail ?? response.senderPhone,
        'Recipient Name': response.beneficiaryName,
        'Recipient Account':
            response.beneficiaryEmail ?? response.beneficiaryPhone,
        if (response.currency == 'USD')
          'Amount in USD': '${response.acceptedAmount} USD',
        if (response.currency == 'USD')
          'Exchange Rate': '1\$ = ${response.exchangedRate} ETB ',
        if (response.currency == 'USD')
          'Amount in ETB': '${response.paidAmount} ETB',
        if (response.currency == 'ETB')
          'Amount in ETB': '${response.acceptedAmount} ETB',
        'Service Charge': '${response.serviceFee} ${response.currency}',
        'VAT': '${response.vat} ${response.currency}',
        'Date': AppMapper.safeFormattedDate(response.createdAt),
      },
      originalCurrency: response.currency,
      totalAmount: response.totalAmount,
      billAmount: response.acceptedAmount ?? response.billAmount,
      requiresOtp: response.authorizationType == 'PIN_AND_OTP',
      billRefNo: response.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      totalAmount: transaction.totalAmount,
      billAmount: transaction.acceptedAmount ?? transaction.billAmount,
      originalCurrency: transaction.originalCurrency,
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      status: 'Paid',
      title: 'Money request payment completed successfully',
      {
        'Transaction Type': 'Money Request Payment',
        'Sender Name': transaction.senderName,
        'Sender Account': transaction.senderEmail ?? transaction.senderPhone,
        'Recipient Name': transaction.beneficiaryName,
        'Recipient Account':
            transaction.beneficiaryEmail ?? transaction.beneficiaryPhone,

        // 'Sender Name': transaction.senderName,
        // if (transaction.senderEmail?.isNotEmpty ?? false)
        //   'Sender Account': transaction.senderEmail
        // else
        //   'Sender Account': transaction.senderPhone,
        // 'Recipient Name': transaction.beneficiaryName,

        // if (transaction.beneficiaryEmail?.isNotEmpty ?? false)
        //   'Recipient Account': transaction.beneficiaryEmail
        // else
        //   'Recipient Account': transaction.beneficiaryPhone,

        //widget.moneyRequest.senderEmail,
        if (transaction.originalCurrency == 'USD')
          'Amount in USD':
              '${transaction.acceptedAmount} ${transaction.originalCurrency}',
        if (transaction.originalCurrency == 'USD')
          'Exchange Rate': '1\$ = ${transaction.exchangeRate} ETB',

        if (transaction.originalCurrency == 'USD')
          'Amount in ETB': '${transaction.paidAmount} ETB',

        if (transaction.originalCurrency == 'ETB')
          'Amount': '${transaction.acceptedAmount} ETB',
        'Service Charge':
            '${transaction.serviceCharge} ${transaction.originalCurrency}',
        'VAT': '${transaction.vat} ${transaction.originalCurrency}',
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),
        'Connect Ref No': transaction.walletFTNumber,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        context: context,
        title: 'Accept Money Request',
      ),
      body: SafeArea(
        child: MultiBlocListener(
          listeners: [
            BlocListener<TransactionBloc, TransactionState>(
              listenWhen: (previous, current) =>
                  current is ConfirmTransferSuccess ||
                  current is ConfirmTransferError,
              listener: (context, state) {
                if (state is ConfirmTransferError) {
                  CustomToastification(context, message: state.message);
                }
              },
            ),
            BlocListener<WalletTransferBloc, WalletTransferState>(
              listenWhen: (previous, current) =>
                  // current is CheckingWalletTransferRules
                  // ||
                  current is WalletTransferRulesChecked ||
                  current is WalletTransferError,
              listener: (context, state) {
            
                
                  if (state is WalletTransferRulesChecked) {
                  setState(() => _isLoading = false);
                  // Create a WalletTransferResponse from the rules data
                
                

                  // _showConfirmScreenBottomSheet(transferResponse);
                } else if (state is WalletTransferError) {
                  setState(() => _isLoading = false);
                  CustomToastification(
                    context,
                    message: state.message,
                  );
                }
              },
            ),
            BlocListener<MoneyRequestDetailBloc, MoneyRequestDetailState>(
              listenWhen: (previous, current) =>
                  current is GetWalletDetailState ||
                  current is MoneyRequestDetailErrorState ||
                  current is MoneyRequestDetailActionState,
              listener: (context, state) {
                if (state is MoneyRequestDetailErrorState) {
                  setState(() => _isLoading = false);
                  CustomToastification(
                    context,
                    message: state.message,
                  );
                } else if (state is MoneyRequestDetailActionState) {
                  setState(() => _isLoading = false);
                  if (state.isConfirming) {
                    if (state.moneyRequestEntity != null) {
                      _showConfirmScreenBottomSheet(state.moneyRequestEntity!);
                    }
                  } else {
                    CustomToastification(
                      context,
                      message: '${state.message} ${state.actionState}',
                    );
                  }
                } else if (state is GetWalletDetailState) {}
              },
            ),
          ],
          child: 
           BlocListener<CheckRuleTransferBloc, CheckRuleTransferState>(
                listener: (context, state) {
                  if (state is CheckingWalletTransferRules) {
                    setState(() => _isLoading = true);
                  }

                  if (state is CheckTransferRulesChecked) {
                    final amount = _currencyController.numericAmount;


                  _handleCustomAmount(amount);
                  } else if (state is CheckTransferRulesFailure) {
                    setState(() {
                      _isLoading = false;
                    });
                    CustomToastification(context, message: state.message);
                  }
                },
            child: CurrencyInputWidget(
              controller: _currencyController,
              title: 'Accept Money Request ',
              subtitle: 'Check the requested amount and proceed to '
                  'confirmation. You can adjust the amount if needed.',
              transactionType: 'money_request',
              fetchTransferLimits: false,
              onContinue: _onContinuePressed,
              isLoading: _isLoading,
            ),
          ),
        ),
      ),
    );
  }
}
