import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/domain/usecases/send_money_request_usecase.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_request_money_confirm_request/send_request_money_confirm_request_bloc.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class SendMoneyRequestAddMoneyScreen extends StatefulWidget {
  const SendMoneyRequestAddMoneyScreen({
    required this.member,
    this.fromChatData,
    super.key,
  });

  final MemberLookupEntity member;
  final Map<String, dynamic>? fromChatData;

  @override
  State<SendMoneyRequestAddMoneyScreen> createState() =>
      _SendMoneyRequestAddMoneyScreenState();
}

class _SendMoneyRequestAddMoneyScreenState
    extends State<SendMoneyRequestAddMoneyScreen> {
  late CurrencyInputController _currencyController;
  double amount = 0;
  final TextEditingController _reasonController = TextEditingController();
  bool _isLoading = false;
  late TransactionBottomSheetsManager _bottomSheetsManager;

  String _senderName = 'Rober'; // TODO

  // Chat context getters
  bool get _isFromChat => widget.fromChatData?['fromChat'] == true;
  String? get _conversationId =>
      widget.fromChatData?['conversationId'] as String?;
  String? get _recipientId => widget.fromChatData?['recipientId'] as String?;
  String? get _recipientName =>
      widget.fromChatData?['recipientName'] as String?;

  @override
  void initState() {
    super.initState();

    context.read<HomeBloc>().add(const HomeProfileFetchingEvent());

    _currencyController = CurrencyInputController(
      currencyType: GlobalVariable.currentlySelectedWallet == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: 0,
      ignoreWalletAmountCheck: true,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      isFromMoneyRequest: true,
      onMoneyRequest: _sendMoneyRequest,
      //  () {
      //   CustomToastification(context, message: 'Got money request');
      // },
      pinController: TextEditingController(),
      transactionType: TransactionType.moneyRequest,
      onPinSubmitted: (value) {},
      reasonController: _reasonController,
      onTransactionSuccess: (response) {
        // _showSuccessBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    final amount = _currencyController.numericAmount;

    if (amount <= 0) {
      CustomToastification(
        context,
        message: 'Amount must be greater than 0',
      );
      return;
    }

    _checkTransferRule();
    // Show confirm bottom sheet
  }

  Future<void> _checkTransferRule() async {
    final amount = _currencyController.numericAmount;

    context.read<CheckRuleTransferBloc>().add(
          CheckWalletTransferRulesRequested(
            amount: amount,
            currency: _currencyController.currencyType == CurrencyType.usd
                ? 'USD'
                : 'ETB',
            productType: 'money_request',
          ),
        );
  }

  void _showConfirmRequestBottomSheet() {
    // Reset loading state when showing the confirm sheet
    setState(() {
      _isLoading = false;
    });

    final amount = _currencyController.numericAmount;

    final data = {
      'Transaction Type': 'Money Request',
      'Recipient Name': widget.member.fullName ?? '',
      'Recipient Email': widget.member.emailAddress ?? '',
      'Recipient Phone': widget.member.phoneNumber ?? '',
      'Sender Name': _senderName,
      'Amount':
          "$amount ${_currencyController.currencyType == CurrencyType.usd ? 'USD' : "ETB"}",
      'Date': AppMapper.safeFormattedDate(DateTime.now()),
    };

    // Clear any previous reason text
    _reasonController.clear();
    setState(() {
      _isLoading = false;
    });

    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: data,
      requiresOtp: false,
      billRefNo: '',
      totalAmount: amount,
      billAmount: amount,
      showStatus: false,
      originalCurrency: GlobalVariable.currentlySelectedWallet,
    );
  }

  void _sendMoneyRequest() {
    final amount = _currencyController.numericAmount;

    context.read<SendRequestMoneyConfirmRequestBloc>().add(
          SendRequestMoneyConfirmRequestEvent(
            sendMoneyRequestParam: SendMoneyRequestParam(
              memberId: widget.member.id,
              transactionType: 'money_request',
              currency: GlobalVariable.currentlySelectedWallet,
              amount: amount,
              reason: _reasonController.text,
            ),
          ),
        );
  }

  void _handleChatSuccess(MoneyRequestEntity moneyRequest) {
    // Send message to chat conversation immediately for live reaction
    _sendMoneyRequestMessageToChat(moneyRequest);

    // Show brief success feedback before navigating back
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Money request sent successfully!'),
        duration: Duration(seconds: 1),
        backgroundColor: Colors.green,
      ),
    );

    // Navigate back to chat conversation after brief delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _navigateToChat();
      }
    });
  }

  void _sendMoneyRequestMessageToChat(MoneyRequestEntity moneyRequest) {
    // Send as a money message with complete structured data
    // Note: In money request context, roles are reversed:
    // - senderName (in money request) = person who will send money = receiver of request message
    // - beneficiaryName (in money request) = person requesting money = sender of request message
    context.read<ChatBloc>().add(
          SendMoneyMessageEvent(
            conversationId: _conversationId!,
            amount: moneyRequest.billAmount,
            currency: moneyRequest.currency,
            reason: _reasonController.text.isNotEmpty
                ? _reasonController.text
                : 'Money request',
            status: 'pending', // Money requests start as pending
            senderName: moneyRequest
                .beneficiaryName, // Person requesting money (chat message sender)
            receiverName: moneyRequest
                .senderName, // Person who will send money (chat message receiver)
            transactionId: moneyRequest.transactionId,
            billRefNo: moneyRequest.billRefNo,
          ),
        );
  }

  void _navigateToChat() {
    if (_isFromChat && _conversationId != null) {
      // Navigate back to chat conversation using proper route
      // This ensures we return to the exact chat conversation
      context.goNamed(
        AppRouteName.chatInterface,
        pathParameters: {'conversationId': _conversationId!},
      );
    }
  }

  void _showSuccessBottomSheet(MoneyRequestEntity moneyRequest) {
    final data = {
      'Transaction Type': 'Money Request',
      'Recipient Name': widget.member.fullName ?? '',
      if (widget.member.emailAddress.isNotEmpty)
        'Recipient Email': widget.member.emailAddress ?? '',
      if (widget.member.phoneNumber.isNotEmpty)
        'Recipient Phone': widget.member.phoneNumber ?? '',
      'Sender Name': _senderName,
      'Amount':
          '${AppMapper.safeFormattedNumberWithDecimal(moneyRequest.billAmount)} ${moneyRequest.currency}',
      'Date': AppMapper.safeFormattedDate(moneyRequest.createdAt),
      'reason': _reasonController.text.isNotEmpty
          ? _reasonController.text
          : moneyRequest.reason,
    };

    _bottomSheetsManager.showSuccessScreenBottomSheet(
      data,
      status: 'Requested',
      transactionId: '',
      billRefNo: '',
      totalAmount: moneyRequest.totalAmount,
      billAmount: moneyRequest.billAmount,
      originalCurrency: moneyRequest.currency,
      showActionButtons: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is HomeProfileLoadedState) {
          _senderName = state.localUser.fullName;

          // CustomToastification(context, message: state.localUser.fullName);
        }
      },
      child: BlocConsumer<SendRequestMoneyConfirmRequestBloc,
          SendRequestMoneyConfirmRequestState>(
        listener: (context, state) {
          if (state is SendRequestMoneyResultState) {
            Navigator.pop(context);
            setState(() {
              _isLoading = false;
            });

            // Handle chat integration if request came from chat
            if (_isFromChat && _conversationId != null) {
              _handleChatSuccess(state.moneyRequest);
            } else {
              _showSuccessBottomSheet(state.moneyRequest);
            }
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: CustomAppBar(
              context: context,
              title: 'Send Money Request',
            ),
            body: SafeArea(
              child:
                  BlocListener<CheckRuleTransferBloc, CheckRuleTransferState>(
                listener: (context, state) {
                  if (state is CheckingWalletTransferRules) {
                    setState(() => _isLoading = true);
                  }

                  if (state is CheckTransferRulesChecked) {
                    _showConfirmRequestBottomSheet();
                  } else if (state is CheckTransferRulesFailure) {
                    setState(() {
                      _isLoading = false;
                    });
                    CustomToastification(context, message: state.message);
                  }
                },
                child: CurrencyInputWidget(
                  controller: _currencyController,
                  title: 'Send Money Request',
                  subtitle: 'Send money requests to your relatives, '
                      'and receive funds in your wallet.',
                  transactionType: 'money_request',
                  onContinue: _onContinuePressed,
                  isLoading: _isLoading,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
