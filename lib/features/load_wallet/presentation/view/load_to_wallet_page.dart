import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_event.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_state.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_bloc.dart';
import 'package:cbrs/features/load_wallet/presentation/view/Load_to_wallet_web_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoadToWalletPage extends StatefulWidget {
  const LoadToWalletPage({super.key});

  @override
  State<LoadToWalletPage> createState() => _LoadToWalletPageState();
}

class _LoadToWalletPageState extends State<LoadToWalletPage> {
  late CurrencyInputController _currencyController;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.usd,
      maxBalance: double.maxFinite,
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    if (_isProcessing) return;

    final amount = _currencyController.numericAmount;

    if (amount <= 0) {
      CustomToastification(
        context,
        message: 'Amount must be greater than 0',
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    _checkTransferRule();
  }

  Future<void> _checkTransferRule() async {
    final amount = _currencyController.numericAmount;

    context.read<CheckRuleTransferBloc>().add(
          CheckWalletTransferRulesRequested(
            amount: amount,
            currency: _currencyController.currencyType == CurrencyType.usd
                ? 'USD'
                : 'ETB',
            productType: 'load_to_wallet',
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoadWalletBloc, LoadWalletState>(
      listener: (context, state) {
        if (state is LoadWalletSuccess) {
          setState(() {
            _isProcessing = false; // Reset loading state on success
          });

          context.pushNamed(
            AppRouteName.loadToWalletWebView,
            queryParameters: {
              'url': state.sessionId,
              'redirectURL': state.redirectURL,
              'billRefNo': state.billRefNo,
            },
          );

          // Navigator.push(
          //   context,
          //   MaterialPageRoute(
          //     builder: (_) => WebViewPage(
          //       url: state.sessionId,
          //       billRefNo: state.billRefNo,
          //       redirectURL: state.redirectURL,
          //     ),
          //   ),
          // );
        } else if (state is LoadWalletCompleted) {
          setState(() {
            _isProcessing = false; // Reset loading state on success
          });
          // context.pushNamed(
          //   AppRouteName.successPage,
          //   pathParameters: {'amount': state.amount},
          // );
        } else if (state is LoadWalletFailure) {
          setState(() {
            _isProcessing = false; // Reset loading state on success
          });
          context.pushNamed(AppRouteName.failurePage);
        }
      },
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              'Load to Wallet',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          body: SafeArea(
            child: BlocProvider(
              create: (context) => sl<TransactionBloc>(),
              child:
                  BlocListener<CheckRuleTransferBloc, CheckRuleTransferState>(
                listener: (context, state) {
                  if (state is CheckingWalletTransferRules) {
                    setState(() => _isProcessing = true);
                  }

                  if (state is CheckTransferRulesChecked) {
                    final amount = _currencyController.numericAmount;

                    context
                        .read<LoadWalletBloc>()
                        .add(LoadToWalletRequested(amount));
                  } else if (state is CheckTransferRulesFailure) {
                    setState(() {
                      _isProcessing = false;
                    });
                    CustomToastification(context, message: state.message);
                  }
                },
                child: CurrencyInputWidget(
                  controller: _currencyController,
                  transactionType: 'load_wallet',
                  title: 'Load To Wallet',
                  subtitle:
                      'Enter the amount to wallet your account and get it ready for sending to anyone.',
                  isLoading: _isProcessing,
                  onContinue: _onContinuePressed,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
