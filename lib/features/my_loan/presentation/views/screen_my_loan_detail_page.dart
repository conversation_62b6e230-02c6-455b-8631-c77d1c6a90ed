import 'dart:io';
import 'dart:ui';

import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/get_loan_status.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_bloc.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_event.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_state.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/upfront_payment_bloc.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_transaction.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:cbrs/features/my_loan/presentation/views/screen_my_loan.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/_rounded_button.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/carousal_with_controls.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/custom_app_bar.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/apartment_detail.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/approved_loan_info.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/build_completed_loan_hero.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/build_payment_option.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/build_payment_options_row.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/build_payment_term_option.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/car_detail.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/loan_info_tab.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/payment_history_page.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/loan_detail/pending_loan_info.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/my_loan_skeleton_card.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/repayment_bottom_sheets_manager.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/status_button.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;

class ScreenMyLoanDetailPage extends StatefulWidget {
  const ScreenMyLoanDetailPage({
    required this.loanId,
    required this.loanType,
    super.key,
  });
  final String loanId;
  final String loanType;

  @override
  State<ScreenMyLoanDetailPage> createState() => _ScreenMyLoanDetailPageState();
}

class _ScreenMyLoanDetailPageState extends State<ScreenMyLoanDetailPage> {
  late RepaymentBottomSheetsManager _bottomSheetsManager;

  int paymentTermOption = 1; // by default payment term is one month

  String productName = '';
  String selectedTab = 'Loan Info';
  List<String> tabList = [
    'Loan Info',
    '',
  ]; // the second tablist will be added after loan status known.

// for handling tab clicking.
  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
      });
    }
  }

  final TextEditingController _pinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    handleFetchDetail();

    _bottomSheetsManager = RepaymentBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.walletTransfer,
      pinController: _pinController,
      onPinSubmitted: (pin, billRefNo, isRepayment) {
        if (isRepayment) {
          context.read<UpfrontPaymentBloc>().add(
                ConfirmMonthlyRepaymentEvent(
                  loanPaymentId: widget.loanId,
                  billRefNo: billRefNo,
                  transactionType: TransactionType.loanRepayment,
                  months: paymentTermOption.toString(),
                  pin: pin,
                ),
              );
        } else {
          context.read<UpfrontPaymentBloc>().add(
                ConfirmTransferEvent(
                  billRefNo: billRefNo,
                  transactionType: TransactionType.upfrontPayment,
                  pin: pin,
                ),
              );
        }
      },
      onTransactionSuccess: (success, isRepayment) {
        Navigator.pop(context);

        if (isRepayment) {
          showRepaymentSuccessBottomSheet(
            success as MonthlyRepaymentTransactionEntity,
          );
        } else {
          showUpfrontSuccessBottomSheet(success as UpfrontLoanEntity);
        }
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

// Used to fetch specific loan with payment history graphs
  Future<void> handleFetchDetail() async {
    context.read<RepaymentBloc>().add(
          FetchLoanInfoEventEvent(
            loanId: widget.loanId,
            months: paymentTermOption.toString(),
          ),
        );
  }

  Future<void> requestUpfrontRepayment() async {
    context.read<UpfrontPaymentBloc>().add(
          PayUpfrontPaymentEvent(
            loanId: widget.loanId,
          ),
        );
  }

  Future<void> _requestMonthlyRepayment() async {
    context.read<UpfrontPaymentBloc>().add(
          GenerateMonthlyRepaymentEvent(
            loanId: widget.loanId,
            months: paymentTermOption.toString(),
          ),
        );
  }

  void showPaymentOptions(
    BuildContext context,
    LoanRepaymentDataEntity loanApplication, {
    bool isPaymentTermOption = false,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Stack(
          children: [
            GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
                child: Container(
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                padding: EdgeInsets.only(
                  top: 22.h,
                  left: 16.w,
                  right: 16.w,
                  bottom: 24.h,
                ),
                clipBehavior: Clip.antiAlias,
                decoration: const ShapeDecoration(
                  color: Color(0xFFFCFCFC),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                ),
                child: isPaymentTermOption
                    ? BuildPaymentTermOption(
                        selectedTerm: paymentTermOption,
                        onTermSelected: (term) {
                          final lastTerm = paymentTermOption;
                          setState(() {
                            paymentTermOption = term;
                          });

                          if (lastTerm != term) {
                            handleFetchDetail();
                          }

                          debugPrint('selected term:: $term');
                        },
                      )
                    : BuildPaymentOption(
                        loanApplication: loanApplication,
                        loanId: widget.loanId,
                        loanType: widget.loanType,
                        months: paymentTermOption.toString(),
                        payWithConnectWallet: () {
                          _requestMonthlyRepayment();
                        },
                      ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(title: 'Loan Details'),
      body: SafeArea(child: _buildBody(context, theme)),
    );
  }

  Widget _buildBody(BuildContext context, ThemeData theme) {
    return BlocConsumer<RepaymentBloc, RepaymentLoanState>(
      listener: (context, state) {
        if (state is RepaymentInfoLoadedState) {
          setState(() {
            productName =
                state.loanDetails.loanRepayments.productDetails?.name ?? '';
          });
        }
      },
      builder: (context, state) {
        if (state is RepaymentLoadingState) {
          return _buildBodySkeleton(context);

          //  const Center(child: const CircularProgressIndicator());
        } else if (state is RepaymentInfoLoadedState) {
          // Filter loans based on the loanStatus and 'approved' status

          final loanItems = state.loanDetails.loanRepayments;
          final loanStatus =
              //'approved';
              getLoanStatusMessage(
            state.loanDetails.loanRepayments.loanInfo?.status ?? '',
          );

// adding the late tab after api Call
          tabList[1] = loanStatus.toLowerCase() != 'pending'
              ? 'Payment History'
              : widget.loanType == 'car'
                  ? 'Car Details'
                  : 'Property Detail';

          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    children: [
                      if (loanStatus == 'completed')
                        BuildCompletedLoanHero(
                          loanType: widget.loanType.toLowerCase(),
                        )
                      else
                        (loanItems.productDetails?.galleryImages?.length ?? 0) >
                                0
                            ? Hero(
                                tag: widget.loanId,
                                child: CarouselWithControls(
                                  imageUrls:
                                      loanItems.productDetails?.galleryImages ??
                                          [],
                                ),
                              )
                            : Container(),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.0.w),
                        child: buildRowLoanNameStatus(
                          context,
                          loanStatus,
                          loanItems,
                        ),
                      ),

                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0.w),
                        child: CustomRoundedTabs(
                          onTap: onTap,
                          selectedTab: selectedTab,
                          tabList: tabList,
                        ),
                      ),
                      if (loanItems.currentBill?.isActive ?? false)
                        //   showPaymentOptions(context, loan, isPaymentTermOption: true);
                        BuildPaymentOptionsRow(
                          onTap: () {
                            showPaymentOptions(
                              context,
                              loanItems,
                              isPaymentTermOption: true,
                            );
                          },
                          paymentTerm: paymentTermOption,
                        ),

                      //   _buildPaymentOption(context, loanItems),
                      _tabView(loanStatus, loanItems),
                      SizedBox(
                        height: 20.h,
                      ),
                    ],
                  ),
                ),
              ),
              if (loanStatus.toLowerCase() == 'active' ||
                  loanStatus.toLowerCase() == 'approved')
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0XFF001C1A).withOpacity(0.05),
                        blurRadius: 3,
                        offset: const Offset(0, -1),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.only(
                    left: 16.w,
                    right: 16.h,
                    top: 8,
                    bottom: 4,
                  ),
                  child: _buildBottomNavigationBar(
                    theme,
                    loanStatus,
                    loanItems.currentBill?.isActive ?? false,
                    loanItems,
                  ),
                ),
            ],
          );
        } else {
          return CustomErrorRetry(
            onTap: handleFetchDetail,
            errorMessage:
                'Failed to fetch Loan detail. Please try again later.',
          );
        }
      },
    );
  }

  Widget _buildBottomNavigationBar(
    ThemeData theme,
    String loanStatus,
    bool isActivePayment,
    LoanRepaymentDataEntity loan,
  ) {
    return BlocConsumer<UpfrontPaymentBloc, RepaymentLoanState>(
      listener: (context, state) {
        if (state is GeneratedUpfronPayment) {
          showUpfrontConfirmBottomSheet(state.upfrontTransactionEntity);
          // CustomToastification(context, message: 'sow confirm', isError: false);
        } else if (state is GenerateMonthlyRepaymentState) {
          showRepaymentConfirmBottomSheet(state.monthlyRepayment);
          // CustomToastification(context,
          //     message: 'sow confirm GenerateMonthlyRepaymentState',
          //     isError: false);
        }
      },
      builder: (context, state) {
        return CustomRoundedBtn(
          btnText: loanStatus.toLowerCase() == 'active'
              ? 'Repay'
              : 'Pay Upfront Payment',
          onTap: () async {
            if (loanStatus.toLowerCase() == 'active' && isActivePayment) {
              showPaymentOptions(context, loan);
            } else if (loanStatus.toLowerCase() == 'approved') {
              requestUpfrontRepayment();
              //  context.pop(true);
              // await context.pushNamed(
              //   AppRouteName.confirmRepay,
              //   extra: {
              //     'loanApplication': loan,
              //     'loanId': widget.loanId,
              //     'loanType': widget.loanType,
              //     'isRepayment': loanStatus.toLowerCase() == 'active',
              //     'months': paymentTermOption.toString(),
              //   },
              // );
            }
          },
          isBtnActive: loanStatus.toLowerCase() == 'approved' ||
              loanStatus.toLowerCase() == 'active' && isActivePayment,
          isLoading: state is RepaymentLoadingState,
        );
      },
    );
  }

  Widget _tabView(String loanStatus, LoanRepaymentDataEntity loan) {
    Widget getTabContent() {
      switch (selectedTab) {
        case 'Loan Info':
          switch (loanStatus.toLowerCase()) {
            case 'active':
              // return const ApprovedLoanInfo();

              return LoanInfoTab(
                loanStatus: loanStatus,
                loanType: widget.loanType,
                hasActivePayment: loan.currentBill?.isActive ?? false,
                hasPenal: loan.currentBill?.isInPenalty ?? false,
                loan: loan,
                months: paymentTermOption.toString(),
              );

            case 'approved':
              return ApprovedLoanInfo(
                loan: loan,
                loanType: widget.loanType.toLowerCase(),
              );
            case 'preparing':
            case 'pending':
              return PendingLoanInfo(pendedLoan: loan);
            case 'completed':
              return ApprovedLoanInfo(
                loan: loan,
                loanType: widget.loanType.toLowerCase(),
              );

            // return LoanInfoTab(
            //   loanStatus: widget.loanStatus,
            //   loanType: widget.loanType,
            //   hasCompleted: true,
            //   hasActivePayment: widget.hasActivePayment,
            //   hasPenal: widget.hasPenal,
            //   loanApplication: widget.loanApplication,

            // );
            default:
              return _buildLoanInfoContent();
          }
        default:
          if (loanStatus.toLowerCase() == 'active' ||
              loanStatus.toLowerCase() == 'completed' ||
              loanStatus.toLowerCase() == 'approved' ||
              loanStatus.toLowerCase() == 'preparing') {
            return PaymentHistoryPage(
              loanPaymentId: widget.loanId,
            );
          }
          // else if (loanStatus.toLowerCase() == 'approved') {
          //   return const ApprovedPaymentHistory();
          // }
          else {
            debugPrint(
              'Amenity images ${loan.productDetails?.amenityData?.length}',
            );

            return widget.loanType.toLowerCase() == 'car'
                ? CarDetail(
                    loanStatus: loanStatus,
                    loanType: widget.loanType,
                    repaymentData: loan,
                  )
                : ApartmentDetail(
                    loanStatus: loanStatus,
                    loanType: widget.loanType,
                    pendedLoan: loan,
                    amenityData: loan.productDetails?.amenityData ?? [],
                  );
          }
      }
    }

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: getTabContent(),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }

  Widget buildRowLoanNameStatus(
    BuildContext context,
    String loanStatus,
    LoanRepaymentDataEntity loan,
  ) {
    return Container(
      margin: EdgeInsets.only(top: 15.h, bottom: 20.h),
      decoration: const BoxDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: CustomBuildText(
                  text:
                      // "hello how are u get this apartmentnet",
                      '${loan.productDetails?.name}',
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              CustomBuildText(
                text: widget.loanType == 'mortgage'
                    ? '\$${loan.productDetails?.amount}'
                    : '\$${loan.productDetails?.price}',
                fontSize: 18.sp,
                fontWeight: FontWeight.w700,
                maxLines: 2,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _subInfo(loan),
              SizedBox(
                width: 8.w,
              ),
              if (loanStatus.toLowerCase() != 'active')
                StatusButton(
                  status: loanStatus,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _subInfo(LoanRepaymentDataEntity loan) {
    return Expanded(
      child: widget.loanType == 'car'
          ? Row(
              children: [
                CustomBuildText(
                  text: '${loan.productDetails?.manufactureYear} Model',
                  fontSize: 12.sp,
                  color: Colors.black.withOpacity(0.5),
                ),
                SizedBox(
                  width: 6.w,
                ),
                RoundedButton(
                  text: loan.productDetails?.model?.toLowerCase() ?? '',
                  // bgColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  textColor: Theme.of(context).primaryColor,

                  // textColor: const Color(0xFF00A1B8),
                  //  textColor: Colors.white,
                  bgColor: const Color(0xFFCCDBF3),
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 1.h),
                  caseType: '',
                  fontSize: 12.sp,
                ),
              ],
            )
          : Row(
              children: [
                Image.asset(
                  MediaRes.zondiconsLocation,
                  width: 20.w,
                  height: 20.h,
                ),
                SizedBox(
                  width: 8.w,
                ),
                Expanded(
                  child: buildText(
                    text: '${loan.productDetails?.location?.fieldName}',
                    fontSize: 14.sp,
                    color: Colors.black.withOpacity(0.5),
                  ),
                ),
              ],
            ),
    );
  }

// for pended loans
  Widget _buildLoanInfoContent() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              'Mortgage Information',
              style: GoogleFonts.outfit(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF2C2B34).withOpacity(0.04),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildLoanInfoItem(
                  title: 'Upfront Payment',
                  value: r'25% $(20,000)',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Loan Period',
                  value: '60 months',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Monthly Payment',
                  value: r'25% $(20,000)',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Interest Rate',
                  value: '5.9%',
                  showDivider: false,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoanInfoItem({
    required String title,
    required String value,
    required bool showDivider,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title.toUpperCase(),
                style: GoogleFonts.outfit(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.outfit(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
        if (showDivider)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Divider(
              color: const Color(0xFF2C2B34).withOpacity(0.03),
              height: 1,
            ),
          ),
      ],
    );
  }

  //build text
  Widget buildText({
    required String text,
    Color color = Colors.black,
    FontWeight fontWeight = FontWeight.w400,
    double fontSize = 14,
  }) {
    return Text(
      text,
      style: GoogleFonts.outfit(
        color: color,
        fontSize: fontSize,
        fontWeight: fontWeight,
      ),
    );
  }

  // build skeleton - loader
  Widget _buildBodySkeleton(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        child: Column(
          children: [
            _rowExpandedSkeleton(height: 270.h),
            SizedBox(
              height: 16.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: MyLoanSkeletonCard(
                          width: 100.w,
                          height: 24.h,
                          borderRadius: 4.r,
                        ),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      MyLoanSkeletonCard(
                        width: 80.w,
                        height: 32.h,
                        borderRadius: 64.r,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          MyLoanSkeletonCard(
                            width: 100.w,
                            height: 24.h,
                            borderRadius: 4.r,
                          ),
                          SizedBox(
                            width: 16.w,
                          ),
                          MyLoanSkeletonCard(
                            width: 80.w,
                            height: 32.h,
                            borderRadius: 64.r,
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      MyLoanSkeletonCard(
                        width: 50.w,
                        height: 16.h,
                        borderRadius: 4.r,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Container(
                    margin: EdgeInsets.only(bottom: 16.h, left: 8, right: 8),
                    padding: EdgeInsets.all(8.r),
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 24,
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: MyLoanSkeletonCard(
                            height: 50.h,
                          ),
                        ),
                        Expanded(
                          child: MyLoanSkeletonCard(
                            height: 50.h,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  _rowExpandedSkeleton(),
                  SizedBox(
                    height: 16.h,
                  ),
                  _rowExpandedSkeleton(),
                  SizedBox(
                    height: 16.h,
                  ),
                  Column(
                    children: [
                      SizedBox(
                        height: 16.h,
                      ),
                      _rowExpandedSkeleton(),
                      Divider(
                        color: Colors.grey.shade300,
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      SizedBox(
                        height: 16.h,
                      ),
                      _rowExpandedSkeleton(),
                      Divider(
                        color: Colors.grey.shade300,
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      SizedBox(
                        height: 16.h,
                      ),
                      _rowExpandedSkeleton(),
                      Divider(
                        color: Colors.grey.shade300,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _rowExpandedSkeleton({double height = 24}) {
    return Row(
      children: [
        Expanded(
          child: MyLoanSkeletonCard(
            height: height.h,
          ),
        ),
      ],
    );
  }

  void showUpfrontConfirmBottomSheet(UpfrontTransactionEntity upfront) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Upfront Payment',
        'Customer Name': upfront.senderName,
        'Bank Name': upfront.bankName,
        'Loan Type': upfront.loanType,
        if (widget.loanType == 'car')
          'Car Name': productName
        else
          'House Name': productName,
        'Down Payment Amount': '${upfront.billAmount} USD',
        'Facilitation Fee': '${upfront.facilitationFee} USD', // todo
      },
      totalAmount: upfront.paidAmount,
      billAmount: upfront.billAmount,
      billRefNo: upfront.billRefNo,
      transactionType: TransactionType.upfrontPayment,
      originalCurrency: 'USD',
      requiresOtp: upfront.authorizationType == 'PIN_AND_OTP',
    );
  }

  void showUpfrontSuccessBottomSheet(UpfrontLoanEntity upfront) {
    final transaction = upfront.upfrontPaymentTransaction;

    _bottomSheetsManager.showSuccessScreenBottomSheet(
      {
        'Transaction Type': 'Upfront Payment',
        'Customer Name': transaction.senderName,
        'Bank Name': transaction.bankName,
        'Loan Type': transaction.loanType,
        if (widget.loanType == 'car')
          'Car Name': productName
        else
          'House Name': productName,
        'Down Payment Amount': '${transaction.billAmount} USD',
        'Facilitation Fee': '${transaction.facilitationFee} USD',
      },

      loanReciept: LoanReceipt.upfrontPayment,
      billRefNo: transaction.billRefNo,
      transactionId: transaction.billRefNo,
      totalAmount: transaction.paidAmount,
      billAmount: transaction.billAmount,

      // transactionType: upfront.upfrontPaymentTransaction.transactionType,
      originalCurrency: 'USD', // TODO
    );
  }

// repayment

  void showRepaymentConfirmBottomSheet(MonthlyRepaymentEntity monthly) {
    final blocValue = BlocProvider.of<UpfrontPaymentBloc>(context);

    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Monthly Repayment',
        'Customer Name': monthly.senderName,
        'Bank Name': monthly.bankName,
        'Loan Type': widget.loanType,
        if (widget.loanType == 'car')
          'Car Name': productName
        else
          'House Name': productName,
        'Repayment Amount': '${monthly.billAmount} USD',
        'Repayment Month': '$paymentTermOption Month',
        'Facilitation Fee': '${monthly.facilitationFee} USD',
        'Interest Amount': '${monthly.interestAmount} USD',
        if (monthly.penaltyAmount != 0.0)
          'Penalty Amount': '${monthly.penaltyAmount} USD',
      },
      isRepayment: true,
      totalAmount: monthly.totalAmount,
      billAmount: monthly.billAmount,
      billRefNo: monthly.billRefNo,
      transactionType: TransactionType.upfrontPayment,
      originalCurrency: 'USD',
      requiresOtp: monthly.authorizationType == 'PIN_AND_OTP',
    );
  }

  void showRepaymentSuccessBottomSheet(
    MonthlyRepaymentTransactionEntity monthlySuccess,
  ) {
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      {
        'Transaction Type': 'Monthly Repayment',
        'Customer Name': monthlySuccess.senderName,
        'Bank Name': monthlySuccess.bankName,
        'Loan Type': widget.loanType,
        if (widget.loanType == 'car')
          'Car Name': productName
        else
          'House Name': productName,
        'Repayment Amount': '${monthlySuccess.billAmount} USD',
        'Repayment Month': '$paymentTermOption Month',
        'Facilitation Fee': '${monthlySuccess.facilitationFee} USD',
        'Interest Amount': '${monthlySuccess.interestAmount} USD',
        if (monthlySuccess.penaltyAmount != 0.0)
          'Penality Amount': '${monthlySuccess.penaltyAmount} USD',
      },
      loanReciept: LoanReceipt.repayment,

      billRefNo: monthlySuccess.billRefNo,
      transactionId: monthlySuccess.billRefNo,
      totalAmount: monthlySuccess.totalAmount,
      billAmount: monthlySuccess.billAmount,

      // transactionType: upfront.upfrontPaymentTransaction.transactionType,
      originalCurrency: 'USD', // TODO
    );
  }
}
