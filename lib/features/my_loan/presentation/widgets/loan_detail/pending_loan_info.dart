import 'package:cbrs/core/common/widgets/custom_menu_screen_cards.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/build_car_details_item.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';

class PendingLoanInfo extends StatelessWidget {
  const PendingLoanInfo({required this.pendedLoan, super.key});
  final LoanRepaymentDataEntity pendedLoan;

  Future<void> _applicationFeeTransaction(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) => CustomPagePadding(
        bottom: 16,
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  height: 4,
                  width: 48,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              const CustomPageHeader(
                   pageTitle: 'Applications Payment',
            description:
                'Details of the applications your loan application payment.',
            
              ),
              const SizedBox(
                height: 16,
              ),
              Container(
                padding: const EdgeInsets.only(top: 12, bottom: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F3F3),
                  borderRadius: BorderRadius.circular(
                    12,
                  ),
                ),
                child: Column(

                  children: [

                    /*
                    Transaction Type
Customer Name 
Customer Account 
Bank Name 
Loan Type
Car or House Name 
Transaction Ref 
Application Code
Application Fee Amount 
Date 
Transaction Ref 
Total Amount  



                    */
                    BuildCarDetailsItem(
                      title: 'Bank Name:',
                      value: '${pendedLoan.bankName}',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Down Payment:',
                      value: '${pendedLoan.loanInfo?.downPayment?.percentage}%',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Upfront Payment Amount:',
                      value: '\$${pendedLoan.loanInfo?.downPayment?.amount}',
                      showDivider: true,
                    ),
                    const BuildCarDetailsItem(
                      title: 'Payment Term:',
                      value: '1 Month',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Loan Period:',
                      value:
                          '${((pendedLoan.loanInfo?.loanPeriod ?? 0) / 12).toStringAsFixed(0)} years',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Payment Amount:',
                      value: '\$${pendedLoan.loanInfo?.monthlyRepayment}/Month',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Interest rate:',
                      value: '${pendedLoan.loanInfo?.interestRate}%',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Daily Penalty Fee:',
                      value: '${pendedLoan.loanInfo?.dailyPenaltyFee}',
                      showDivider: true,
                    ),
                    BuildCarDetailsItem(
                      title: 'Facilitation Fee:',
                      value: '${pendedLoan.loanInfo?.facilitationFee}',
                      showDivider: false,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              CustomRoundedBtn(
                btnText: 'Back',
                isLoading: false,
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Loan Payment Information',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(
            height: 2,
          ),
          CustomBuildText(
            text: 'Details information about car loan Application',
            color: Colors.black.withOpacity(0.3),
            fontSize: 14.sp,
          ),
          SizedBox(
            height: 12.h,
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 12.h),
            decoration: BoxDecoration(
              color: const Color(0xFF2C2B34).withOpacity(0.04),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                BuildCarDetailsItem(
                  title: 'Bank Name:',
                  value: '${pendedLoan.bankName}',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Down Payment:',
                  value: '${pendedLoan.loanInfo?.downPayment?.percentage}%',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Upfront Payment Amount:',
                  value: '\$${pendedLoan.loanInfo?.downPayment?.amount}',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Payment Term:',
                  value: '1 Month',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Loan Period:',
                  value:
                      '${((pendedLoan.loanInfo?.loanPeriod ?? 0) / 12).toStringAsFixed(0)} years',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Payment Amount:',
                  value: '\$${pendedLoan.loanInfo?.monthlyRepayment}/Month',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Interest rate:',
                  value: '${pendedLoan.loanInfo?.interestRate}%',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Daily Penalty Fee:',
                  value: '${pendedLoan.loanInfo?.dailyPenaltyFee}',
                  showDivider: true,
                ),
                BuildCarDetailsItem(
                  title: 'Facilitation Fee:',
                  value: '${pendedLoan.loanInfo?.facilitationFee}',
                  showDivider: false,
                ),
              ],
            ),
          ),
          SizedBox(
            height: 16,
          ),
          CustomMenuScreenCards(
            containerIcon: MediaRes.listIcon,
            iconColor: Theme.of(context).primaryColor,
            iconHeight: 32,
            iconwidth: 32,

            // 'assets/images/empty_transaction_screen_img.png',
            title: 'Applications Payment',
            description:
                'Details of the applications your loan application payment.',
            
            onTap: () {
              _applicationFeeTransaction(context);
            },
          ),
        ],
      ),
    );
  }
}
