import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/top_up/data/data_sources/top_up_remote_data_source.dart';
import 'package:cbrs/features/top_up/data/model/top_up_providers_model.dart';
import 'package:cbrs/features/top_up/data/model/top_up_success_model.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_confirm_response_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_phone_number_lookup_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_wallet_balance_entities.dart';
import 'package:cbrs/features/top_up/domain/repositories/top_up_repositories.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/otp_resend_response.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/cupertino.dart';

class TopUpRepositoriesImpl implements TopUpRepositories {
  const TopUpRepositoriesImpl(this._remoteDataSource);

  final TopUpRemoteDataSource _remoteDataSource;



  @override
  ResultFuture<TopUpSuccessResponseModel> createTopUp({
    required String phoneNumber,
    required int amount,
    required String beneficiaryId,
    required String billReason,

  }) async {
    try {
      final returnData = await _remoteDataSource.createTopUp(
        amount: amount,
        phoneNumber: phoneNumber,
        beneficiaryId: beneficiaryId,
        billReason: billReason
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<TopUpProvidersModel> getTopUpProviders() async {
    try {
      final returnData = await _remoteDataSource.getTopUpProviders();
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      debugPrint('error from confirm pin ${e.message}');
      return Left(ServerFailure.fromException(e));
    }
  }
  

}
