import 'package:equatable/equatable.dart';

abstract class TopUpEvent extends Equatable {}

class CreateTopUpEvent extends TopUpEvent {
  CreateTopUpEvent({required this.phoneNumber, required this.amount, required this.beneficiaryId});
  final String phoneNumber;
  final int amount;
  final String beneficiaryId;

  @override
  List<Object?> get props => [phoneNumber, amount];
}

class GetTopUpProviderEvent extends TopUpEvent {
  GetTopUpProviderEvent();

  @override
  List<Object?> get props => [];
}
