import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_confirm_response_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_phone_number_lookup_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_success_response_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_wallet_balance_entities.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/otp_resend_response.dart';
import 'package:dartz/dartz.dart';

abstract class TopUpRepositories {
  ResultFuture<TopUpSuccessResponseEntities> createTopUp({
    required String phoneNumber,
    required int amount,
    required String beneficiaryId,
    required String billReason,
  });



  ResultFuture<TopUpProvidersEntity> getTopUpProviders();
}
