import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:flutter/material.dart';

class ConfirmTransferResponseModel extends ConfirmTransferResponse {
  const ConfirmTransferResponseModel({
    required super.success,
    required super.statusCode,
    required super.message,
    required TransactionDetailModel super.transaction,
  });

  factory ConfirmTransferResponseModel.fromJson(Map<String, dynamic> json) {
    debugPrint('json of data c ${json['transaction']}');

    return ConfirmTransferResponseModel(
      success: json['success'] == true,
      statusCode: (json['statusCode'] as num?)?.toInt() ?? 500,
      message: json['message']?.toString() ?? 'Unknown error',
      transaction: json['transaction'] != null
          ? TransactionDetailModel.fromJson(
              json['transaction'] as Map<String, dynamic>,
            )
          : TransactionDetailModel.fromJson(
              json['data'] as Map<String, dynamic>,
            ),
    );
  }
}

class TransactionDetailModel extends ConfirmTransferDetail {
  TransactionDetailModel({
    required super.id,
    required this.transactionTypeStr,
    required super.billAmount,
    required super.originalCurrency,
    required super.serviceCharge,
    required super.vat,
    required super.totalAmount,
    required super.paidAmount,
    required super.billRefNo,
    required super.authorizationType,
    required super.status,
    required this.createdAtStr,
    required this.lastModifiedStr,
    required super.acceptedAmount,
    super.amountInEtb,
    super.senderId,
    super.senderName,
    super.senderPhone,
    super.senderEmail,
    super.beneficiaryId,
    super.beneficiaryName,
    super.beneficiaryPhone,
    super.beneficiaryEmail,
    super.beneficiaryAccountNo,
    super.bankName,
    super.bankCode,
    super.bankId,
    super.bankLogo,
    super.changedCurrency,
    super.exchangeRate,
    super.paidDate,
    super.ftNumber,
    super.walletFTNumber,
    super.merchantId,
    super.merchantType,
    super.invoiceURL,
    super.billReason,
    super.mpgsRef,
    super.bankRef,
    super.connectRefNo,
    super.ftReference,
    super.senderAccountNo,
  }) : super(
          transactionType: _parseTransactionType(transactionTypeStr),
          createdAt: DateTime.parse(createdAtStr),
          lastModified: DateTime.parse(lastModifiedStr),
        );

  factory TransactionDetailModel.fromJson(Map<String, dynamic> json) {
    debugPrint("mpgs ${json['mpgsReference']}");
    debugPrint("FTNumber ${json['FTNumber']}");

    return TransactionDetailModel(
      id: json['id'] as String,
      senderId: json['senderId'] as String?,
      senderName: json['senderName'] as String?,
      senderPhone: json['senderPhone'] as String?,
      senderEmail: json['senderEmail'] as String?,
      beneficiaryId: json['beneficiaryId'] as String?,
      beneficiaryName: json['beneficiaryName'] as String?,
      beneficiaryPhone: json['beneficiaryPhone'] as String?,
      beneficiaryEmail: json['beneficiaryEmail'] as String?,
      beneficiaryAccountNo: json['beneficiaryAccountNo'] as String?,
      bankName: json['bankName'] as String?,
      bankCode: json['bankCode'] as String?,
      bankId: json['bankId'] as String?,
      bankLogo: json['bankLogo'] as String?,
      transactionTypeStr: json['transactionType'] as String,
      billAmount: (json['billAmount'] as num).toDouble(),
      acceptedAmount: AppMapper.safeDouble(json['acceptedAmount']),
      originalCurrency: json['originalCurrency'] as String,
      serviceCharge: (json['serviceCharge'] as num?)?.toDouble() ?? 0.0,
      vat: (json['VAT'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      amountInEtb: json['amountInEtb'] != null
          ? (json['amountInEtb'] as num).toDouble()
          : null,
      changedCurrency: json['changedCurrency'] as String?,
      exchangeRate: (json['exchangeRate'] as num?)?.toDouble(),
      paidAmount: (json['paidAmount'] as num).toDouble(),
      billRefNo: json['billRefNo'] as String,
      paidDate: json['paidDate'] as String?,
      ftNumber: json['FTNumber'] as String?,
      walletFTNumber: json['walletFTNumber'] as String?,
      authorizationType: json['authorization_type'] as String? ?? 'PIN',
      status: json['status'] as String,
      createdAtStr: json['createdAt'] as String,
      lastModifiedStr: json['lastModified'] as String,
      merchantId: json['merchantId'] as String?,
      merchantType: json['merchantType'] as String?,
      invoiceURL: json['invoiceURL'] as String?,
      billReason: json['billReason'] as String?,
      mpgsRef: AppMapper.safeString(json['mpgsReference']),
      bankRef: AppMapper.safeString(json['FTNumber']),
      connectRefNo: AppMapper.safeString(json['connectRefNo']),
      ftReference: AppMapper.safeString(json['FTNumber']),
      senderAccountNo: AppMapper.safeString(json['senderAccountNo']),

      
    );
  }
  final String transactionTypeStr;
  final String createdAtStr;
  final String lastModifiedStr;

  static TransactionType _parseTransactionType(String typeStr) {
    try {
      return TransactionType.fromString(typeStr);
    } catch (_) {
      return TransactionType.bankTransfer;
    }
  }
}
