// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TransactionMapData {
  final Transaction transaction;

  TransactionMapData(this.transaction);

  String get billAmount =>
      getFormattedAmountDisplay(amount: transaction.billAmount);

  String get totalAmount =>
      getFormattedAmountDisplay(amount: transaction.totalAmount ?? 0.0);

  String get acceptedAmount =>
      getFormattedAmountDisplay(amount: transaction.acceptedAmount ?? 0.0);
  String get paidAmount => getFormattedAmountDisplay(
        amount: transaction.paidAmount,
        hasCurrency: true,
        customCurrency: 'ETB',
      );
  String get serviceCharge =>
      getFormattedAmountDisplay(amount: transaction.serviceCharge ?? 0.0);
  String get vat => getFormattedAmountDisplay(amount: transaction.vat ?? 0.0);

  String get formattedDate =>
      AppMapper.safeFormattedDate(transaction.createdAt);

  //_dateFormatter.format(transaction.createdAt);
  String get _label => getTransactionLabel();
  String get billReason => transaction.billReason ?? 'unknown';

  /// currency gormating
  String _getCurrencySymbol(String code) {
    switch (code.toUpperCase()) {
      case 'USD':
        return r'$';
      case 'ETB':
        return 'ETB';

      default:
        return code;
    }
  }

  String formatAmount(double amount, String? currencyCode) {
    final symbol = _getCurrencySymbol(currencyCode ?? 'ETB');
    final formatter =
        NumberFormat.currency(locale: 'en_US', symbol: '$symbol ');
    return formatter.format(amount);
  }

  String getFormattedAmountDisplay({
    double amount = 0.0,
    bool hasCurrency = false,
    String customCurrency = '',
  }) {
    final originalFormatted = formatAmount(
      amount,
      hasCurrency ? customCurrency : transaction.originalCurrency,
    );

    return originalFormatted;
  }

  // ---- Transaction label ----

  String getTransactionLabel() {
    switch (transaction.transactionType.toLowerCase()) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'wallet_transfer':
        return 'Wallet Transfer';
      case 'change_to_birr':
        return 'Change To Birr';
      case 'load_to_wallet':
        return 'Load To Wallet';
      case 'add_money':
        return 'Add Money';
      case 'money_request':
        return 'Money Request';

      case 'topup':
        return 'Mobile Top-up';

// laons
      case 'upfront_payment':
        return 'Upfront Payment';
      case 'application_fee':
        return 'Loan Application Fee Payment';

      case 'repayment':
        return 'Repayment';
      case 'bulk_repayment':
        return 'Bulk Repayment';

// _loanRepayment, _bulkRepayment, _overDueLoanRepayment

      case 'merchant_payment':
        switch (billReason.toLowerCase()) {
          case 'airtime':
            return 'Mobile Top-up';
          case 'package':
            return 'Gift Package';
          case 'utility':
            return 'Utility';
          case 'miniapp':
            return 'Mini Apps';

          default:
            return 'Merchant Payment';
        }

      default:
        return 'Unknown Transaction';
    }
  }

  List<Widget> toWidgetList() {
    switch (_label) {
      case 'Bank Transfer':
        return _bankTransferDetails();
      case 'Wallet Transfer':
        return _walletTransferDetails();
      case 'Change To Birr':
        return _changeToBirr();
      case 'Load To Wallet':
        return _loadToWallet();
      case 'Add Money':
        return _addMoney();

      case 'Mobile Top-up':
        return _mobileTopUpDetails();

      case 'Merchant Payment':
        return _merchantPayment();

      case 'Money Request':
        return _moneyRequest();

// _loanRepayment, _bulkRepayment, _overDueLoanRepayment

      case 'Upfront Payment':
        return _upfrontPayment();
      case 'Loan Application Fee Payment':
        return _applicationFeePayment();

      case "Repayment":
        return _loanRepayment();

      default:
        return _defaultDetails(_label);
    }
  }

  // ---- Row builder ----

  Widget _buildContainer({required String label, required String value}) {
    return Container(
      padding: EdgeInsets.only(bottom: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomBuildText(
            text: label,
            fontSize: 14.sp,
            color: const Color(0xFF000000).withOpacity(0.5),
            caseType: 'default',
          ),
          SizedBox(width: 12.w),
          Flexible(
            child: CustomBuildText(
              text: value,
              caseType: '',
              fontSize: 13.sp,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool isStatus = false,
    String valueCase = 'eachWord',
  }) {
    if (!isStatus) {
      return _buildContainer(label: label, value: value);
    }
    if (value.isEmpty) return const SizedBox.shrink();
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomBuildText(
          text: label,
          color: Colors.grey[600]!,
        ),
        const SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFFD4FECB),
            borderRadius: BorderRadius.circular(22),
          ),
          child: CustomBuildText(
            text: value,
            color: const Color(0xFF3EA100),
            caseType: valueCase,
          ),
        ),
      ],
    );
  }

  // ---- Widget List Generator ----

  List<Widget> _bankTransferDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'Unknown Bank'),
        _buildDetailRow('Recipent Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipent Account',
          transaction.beneficiaryAccountNo ?? 'N/A',
        ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in USD', billAmount),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('FT Reference', transaction.ftNumber ?? 'N/A'),
        _buildDetailRow('Transaction Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _walletTransferDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),
        _buildDetailRow('Recipent Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipent Account',
          transaction.beneficiaryEmail ?? transaction.beneficiaryPhone ?? 'N/A',
        ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in USD', billAmount),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('Transaction Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _changeToBirr() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Amount in USD', billAmount ?? 'N/A'),
        _buildDetailRow(
          'Excange Rate',
          '1 USD = ETB${transaction.exchangeRate}',
        ),
        _buildDetailRow('Amount in ETB', paidAmount ?? 'N/A'),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _loadToWallet() => [
        _buildDetailRow('Transaction Type', _label),

        _buildDetailRow('Card Holder Name', transaction.senderName),
        _buildDetailRow(
          'Card Nuber',
          transaction.cardNumber ?? 'N/A',
        ), // masked - TODO
        _buildDetailRow('Recipient Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipient Account',
          transaction.beneficiaryPhone ?? transaction.beneficiaryEmail ?? 'N/A',
        ),
        _buildDetailRow('Amount', billAmount),

        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('MPGS Ref No', transaction.mpgsReference ?? 'N/A'),

        _buildDetailRow('Transaction Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _addMoney() => [
        _buildDetailRow('Transaction Type', _label),

        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account Number',
          transaction.beneficiaryAccountNo ?? 'N/A',
        ),
        _buildDetailRow('Recipient Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipient Account',
          transaction.beneficiaryEmail ?? transaction.beneficiaryPhone ?? 'N/A',
        ),

        _buildDetailRow('Amount in ETB', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow(
          'FT  Reference',
          transaction.ftNumber ?? 'N/A',
        ), // todo

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];
/*
  List<Widget> _giftPackageDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Recipent Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipent Phone',
          transaction.beneficiaryPhone ?? 'N/A',
        ),


        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in USD', billAmount),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];
*/
  List<Widget> _mobileTopUpDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),

        _buildDetailRow(
          'Recipent Name',
          transaction.beneficiaryName ?? 'N/A',
        ),

        _buildDetailRow(
          'Recipent Account Number',
          transaction.beneficiaryConnectCode ?? 'N/A',
        ),
        _buildDetailRow(
          'Purchased To',
          transaction.beneficiaryPhone ?? 'N/A',
        ),
        // merchnat name - safaricom or ethiteleocm
        // _buildDetailRow(
        //   'Recipient Acccount',
        //   transaction.billRefNo ?? 'N/A',
        // ), // merchant aacccount or code
        _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  /// There are different merchant payments.
  ///
  List<Widget> _merchantPayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),

        _buildDetailRow(
          'Recipent Name',
          transaction.beneficiaryName ?? 'N/A',
        ), // merchnat name
        _buildDetailRow(
          'Recipient Acccount',
          transaction.beneficiaryConnectCode ?? 'N/A',
        ), // merchant aacccount or code
        _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _moneyRequest() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),

        _buildDetailRow(
          'Recipent Name',
          transaction.beneficiaryName ?? 'N/A',
        ), // merchnat name
        _buildDetailRow(
          'Recipient Account',
          transaction.beneficiaryEmail?.toLowerCase() ??
              transaction.beneficiaryPhone ??
              'N/A',
          valueCase: '',
        ), // merchant aacccount or code
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Amount in USD',
            ((transaction.overPaid ?? false) ||
                    (transaction.underPaid ?? false))
                ? acceptedAmount
                : billAmount,
          ),

        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow(
            'Amount in ETB',
            ((transaction.overPaid ?? false) ||
                    (transaction.underPaid ?? false))
                ? acceptedAmount
                : billAmount,
          ),

        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _upfrontPayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Customer Name', transaction.senderName ?? 'N/A'),
        _buildDetailRow(
          'Customer Account',
          transaction.senderEmail?.toLowerCase() ??
              transaction.senderPhone ??
              '',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'N/A'),
        _buildDetailRow('Loan Type', '${transaction.loanType}'),
        if (transaction.loanType == 'car')
          _buildDetailRow('Car Name', '${transaction.productDetails?.name}')
        else
          _buildDetailRow('House Name', '${transaction.productDetails?.name}'),
        _buildDetailRow('Transaction Ref', transaction.billRefNo),
        _buildDetailRow('Transaction Status', 'Paid'),
        _buildDetailRow('Down Payment Amount', billAmount),
        _buildDetailRow('Facilitation Fee', "\$${transaction.facilitationFee}"),
        _buildDetailRow('Date', formattedDate),
      ];

  List<Widget> _applicationFeePayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Customer Name', transaction.senderName ?? 'N/A'),
        _buildDetailRow(
          'Customer Account',
          transaction.senderEmail?.toLowerCase() ??
              transaction.senderPhone ??
              '',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'N/A'),
        _buildDetailRow('Loan Type', '${transaction.loanType}'),
        if (transaction.loanType == 'car')
          _buildDetailRow('Car Name', '${transaction.productDetails?.name}')
        else
          _buildDetailRow('House Name', '${transaction.productDetails?.name}'),
        _buildDetailRow('Application fee', billAmount),
        _buildDetailRow('Application Code', transaction.billRefNo),
        _buildDetailRow('Transaction Ref', transaction.billRefNo),
        _buildDetailRow('Transaction Status', 'Paid'),
        _buildDetailRow('Date', formattedDate),
      ];

  List<Widget> _loanRepayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Customer Name', transaction.senderName ?? 'N/A'),
        _buildDetailRow(
          'Customer Account',
          transaction.senderEmail?.toLowerCase() ??
              transaction.senderPhone ??
              '',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'N/A'),
        _buildDetailRow('Loan Type', _label),
        _buildDetailRow('Loan Type', '${transaction.loanType}'),
        if (transaction.loanType == 'car')
          _buildDetailRow('Car Name', '${transaction.productDetails?.name}')
        else
          _buildDetailRow('House Name', '${transaction.productDetails?.name}'),
        _buildDetailRow('Repaymnet Amount', transaction.billRefNo),
        _buildDetailRow('Repayment Month', transaction.billRefNo),
        _buildDetailRow('Facilitation Fee', transaction.billRefNo),
        _buildDetailRow('Transaction Status', transaction.status),
        _buildDetailRow('Transaction Ref', transaction.billRefNo),
        _buildDetailRow('Date', formattedDate),
      ];

  List<Widget> _bulkRepayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Customer Name', transaction.senderName ?? 'N/A'),
        _buildDetailRow(
          'Customer Account',
          transaction.senderEmail?.toLowerCase() ??
              transaction.senderPhone ??
              '',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'N/A'),
        _buildDetailRow('Loan Type', _label),
        _buildDetailRow('Loan Type', '${transaction.loanType}'),
        if (transaction.loanType == 'car')
          _buildDetailRow('Car Name', '${transaction.productDetails?.name}')
        else
          _buildDetailRow('House Name', '${transaction.productDetails?.name}'),
        _buildDetailRow('Repaymnet Amount', transaction.billRefNo),
        _buildDetailRow('Repayment Months', transaction.billRefNo),
        _buildDetailRow('Facilitation Fee', transaction.billRefNo),
        _buildDetailRow('Transaction Status', transaction.status),
        _buildDetailRow('Transaction Ref', transaction.billRefNo),
        _buildDetailRow('Date', formattedDate),
      ];

  List<Widget> _overDueLoanRepayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Customer Name', transaction.senderName ?? 'N/A'),
        _buildDetailRow(
          'Customer Account',
          transaction.senderEmail?.toLowerCase() ??
              transaction.senderPhone ??
              '',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'N/A'),
        _buildDetailRow('Loan Type', _label),
        _buildDetailRow('Loan Type', '${transaction.loanType}'),
        if (transaction.loanType == 'car')
          _buildDetailRow('Car Name', '${transaction.productDetails?.name}')
        else
          _buildDetailRow('House Name', '${transaction.productDetails?.name}'),
        _buildDetailRow('Repaymnet Amount', transaction.billRefNo),
        _buildDetailRow('Repayment Months', transaction.billRefNo),
        _buildDetailRow('Facilitation Fee', transaction.billRefNo),
        _buildDetailRow('Transaction Status', transaction.status),
        _buildDetailRow('Transaction Ref', transaction.billRefNo),
        _buildDetailRow('Date', formattedDate),
      ];

  List<Widget> _defaultDetails(String label) => [
        _buildDetailRow('Transaction ID', transaction.id),
        _buildDetailRow('Type', label),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];
}
