import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/chat/presentation/managers/chat_money_manager.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transactions/presentation/views/quick wallet transfer/quick_pay_recipent_card.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

/// A unified component for handling quick wallet transfers for both USD and ETB currencies.
///
/// This is implemented as a bottom sheet that can be shown from other screens.
class AddAmountQuickWallet extends StatefulWidget {
  const AddAmountQuickWallet({
    required this.walletBalance,
    required this.recipientName,
    required this.senderName,
    required this.currency,
    super.key,
    this.recipientEmail,
    this.recipientPhone,
    this.recipientAvatar,
    this.onClose,
    this.isFromChat = false,
    this.chatContext,
  }) : assert(
          recipientEmail != null || recipientPhone != null,
          'Either recipientEmail or recipientPhone must be provided',
        );
  final double walletBalance;
  final String? recipientEmail;
  final String? recipientPhone;
  final String recipientName;
  final String senderName;
  final String currency;
  final String? recipientAvatar;
  final VoidCallback? onClose;
  final bool isFromChat;
  final Map<String, dynamic>? chatContext;

  /// Shows this component as a bottom sheet.
  static void show(
    BuildContext context, {
    required double walletBalance,
    required String recipientName,
    required String senderName,
    required String currency,
    String? recipientEmail,
    String? recipientPhone,
    String? recipientAvatar,
    bool isFromChat = false,
    Map<String, dynamic>? chatContext,
  }) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          BlocConsumer<WalletTransferBloc, WalletTransferState>(
        listener: (context, state) {
          if (state is WalletTransferError) {
            CustomToastification(
              context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.9,
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(36.r)),
            ),
            child: AddAmountQuickWallet(
              walletBalance: walletBalance,
              recipientEmail: recipientEmail,
              recipientPhone: recipientPhone,
              recipientName: recipientName,
              senderName: senderName,
              currency: currency,
              recipientAvatar: recipientAvatar,
              onClose: () => Navigator.pop(context),
              isFromChat: isFromChat,
              chatContext: chatContext,
            ),
          );
        },
      ),
    );
  }

  @override
  State<AddAmountQuickWallet> createState() => _AddAmountQuickWalletState();
}

class _AddAmountQuickWalletState extends State<AddAmountQuickWallet> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;
  late TransactionBottomSheetsManager _bottomSheetsManager;
  final TextEditingController _pinController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _currencyController = CurrencyInputController(
      currencyType:
          widget.currency == 'USD' ? CurrencyType.usd : CurrencyType.etb,
      maxBalance: widget.walletBalance,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.walletTransfer,
      pinController: _pinController,
      isFromChat: widget.isFromChat,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<WalletTransferBloc>().state
                        as WalletTransferPinRequired)
                    .billRefNo,
                transactionType: tx_type.TransactionType.walletTransfer,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        Navigator.pop(context);
        if (Navigator.canPop(context)) Navigator.pop(context);

        if (widget.isFromChat && widget.chatContext != null) {
          _handleChatTransferSuccess(response);
        } else {
          _showSuccessScreenBottomSheet(response);
        }
      },
      onTransactionComplete: () {
        // This is now a fallback - success screen should handle navigation directly
        if (widget.isFromChat) {
          context.go('/main/main');
        } else {
          context.go(AppRouteName.home);
        }
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }

      // Check transfer rules
      _checkTransferRule();
      // CustomToastification(context, message: 'Update Check transfer here');

      // context.read<WalletTransferBloc>().add(
      //       CheckWalletTransferRulesRequested(
      //         amount: amount,
      //         currency: widget.currency,
      //       ),
      //     );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  Future<void> _checkTransferRule() async {
    final amount = _currencyController.numericAmount;

    context.read<CheckRuleTransferBloc>().add(
          CheckWalletTransferRulesRequested(
            amount: amount,
            currency: _currencyController.currencyType == CurrencyType.usd
                ? 'USD'
                : 'ETB',
            productType: 'wallet_transfer',
          ),
        );
  }

  void _showConfirmScreenBottomSheet(WalletTransferResponse response) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Wallet to Wallet Transfer',
        'Recipient Name': widget.recipientName,
        'Recipient Email': widget.recipientEmail,
        'Recipient Phone': widget.recipientPhone,
        if (response.data.originalCurrency == 'USD')
          'Amount In USD': '${response.data.billAmount} USD',
        if (response.data.originalCurrency == 'USD')
          'Exchange Rate': '1\$ = ${response.data.exchangeRate} ETB',
        if (response.data.originalCurrency == 'USD')
          'Amount In ETB': '${response.data.paidAmount} ETB'
        else
          'Amount In ETB': '${response.data.billAmount} ETB',
        'Service Charge':
            '${response.data.serviceCharge} ${response.data.originalCurrency}',
        'VAT': '${response.data.vat} ${response.data.originalCurrency}',
        'Date': AppMapper.safeFormattedDate(response.data.createdAt),
      },
      totalAmount: response.data.totalAmount,
      billAmount: response.data.billAmount,
      originalCurrency: response.data.originalCurrency,
      requiresOtp:
          WalletTransferAuthResponse.fromJson(response.toJson()).requiresOtp,
      billRefNo: response.data.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      {
        'Transaction Type': 'Wallet to Wallet Transfer',
        'Recipient Name': transaction.beneficiaryName,
        if (transaction.beneficiaryEmail != null)
          'Recipient Email': transaction.beneficiaryEmail,
        if (transaction.beneficiaryPhone != null)
          'Recipient Phone': transaction.beneficiaryPhone,
        if (transaction.originalCurrency == 'USD')
          'Amount In USD': '${transaction.billAmount} USD',
        if (transaction.originalCurrency == 'USD')
          'Exchange Rate': '1\$ = ${transaction.exchangeRate} ETB',
        if (transaction.originalCurrency == 'USD')
          'Amount In ETB': '${transaction.paidAmount} ETB'
        else
          'Amount In ETB': '${transaction.billAmount} ETB',
        'Service Charge':
            '${transaction.serviceCharge} ${transaction.originalCurrency}',
        'VAT': '${transaction.vat} ${transaction.originalCurrency}',
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),
        'Connect Ref No': transaction.walletFTNumber,
        // Add conversation ID for chat navigation
        if (widget.isFromChat && widget.chatContext != null)
          'conversationId': widget.chatContext!['conversationId'],
      },
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      totalAmount: transaction.totalAmount,
      billAmount: transaction.billAmount,
      status: 'Paid',
      originalCurrency: transaction.originalCurrency,
      title: 'Your wallet transfer was successfuly completed.',
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WalletTransferBloc, WalletTransferState>(
      listenWhen: (previous, current) =>
          // current is CheckingWalletTransferRules ||
          current is WalletTransferRulesChecked ||
          current is WalletTransferLoading ||
          current is WalletTransferPinRequired ||
          current is WalletTransferFailure ||
          current is WalletTransferError,
      listener: (context, state) {
        if (state is WalletTransferPinRequired) {
          debugPrint('state is WalletTransferPinRequired');
          setState(() {
            _isLoading = false;
          });
          // Navigator.pop(context);
          _showConfirmScreenBottomSheet(state.response);
        } else if (state is WalletTransferFailure ||
            state is WalletTransferError) {
          setState(() => _isLoading = false);
          CustomToastification(
            context,
            message: state is WalletTransferFailure
                ? state.message
                : (state as WalletTransferError).message,
          );
        }
      },
      buildWhen: (previous, current) =>
          current is WalletTransferInitial ||
          current is WalletTransferLoading ||
          current is WalletDetailsLoaded,
      builder: (context, state) {
        return SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Padding(
                  padding: EdgeInsets.only(top: 12.h, bottom: 16.h),
                  child: Container(
                    width: 60.w,
                    height: 2.h,
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                  ),
                ),
              ),
              const CustomPagePadding(
                child: CustomPageHeader(
                  pageTitle: 'Add Amount ',
                  description:
                      'Enter the amount you wish to send to the recipient and submit.',
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: QuickPayRecipentCard(
                  recipientName: widget.recipientName,
                  recipientPhone: widget.recipientPhone,
                  recipientAvatar: widget.recipientAvatar,
                  recipientEmail: widget.recipientEmail,
                ),
              ),
              Expanded(
                child:
                    BlocListener<CheckRuleTransferBloc, CheckRuleTransferState>(
                  listener: (context, state) {
                    if (state is CheckingWalletTransferRules) {
                      setState(() => _isLoading = true);
                    }

                    if (state is CheckTransferRulesChecked) {
                      final amount = _currencyController.numericAmount;

                      context.read<WalletTransferBloc>().add(
                            TransferToWalletEvent(
                              beneficiaryEmail: widget.recipientEmail,
                              beneficiaryPhone: widget.recipientPhone,
                              // beneficiaryId: "widget.memberInfo.id",
                              amount: _currencyController.numericAmount,
                              currency: widget.currency,
                            ),
                          );
                    } else if (state is CheckTransferRulesFailure) {
                      setState(() {
                        _isLoading = false;
                      });
                      CustomToastification(context, message: state.message);
                    }
                  },
                  child: CurrencyInputWidget(
                    controller: _currencyController,
                    title: '',
                    subtitle: '',
                    isLoading: _isLoading,
                    transactionType: 'wallet_transfer',
                    customBalance: widget.walletBalance,
                    customWalletType: widget.currency,
                    onContinue: _onContinuePressed,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Handle successful transfer when coming from chat
  void _handleChatTransferSuccess(ConfirmTransferResponse response) {
    // Store the transaction response for later use

    // Send success message to chat IMMEDIATELY on transaction success
    final chatContext = widget.chatContext!;
    final conversationId = chatContext['conversationId'] as String;
    final receiverName = chatContext['receiverName'] as String;

    ChatMoneyManager.sendMoneySuccessMessageToChat(
      context: context,
      conversationId: conversationId,
      receiverName: receiverName,
      amount: response.transaction.billAmount,
      currency: response.transaction.originalCurrency,
    );

    // Show success screen with custom callback
    _showSuccessScreenBottomSheet(response);
  }
}
