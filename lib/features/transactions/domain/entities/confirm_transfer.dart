import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:equatable/equatable.dart';

class ConfirmTransferResponse extends Equatable {
  const ConfirmTransferResponse({
    required this.success,
    required this.statusCode,
    required this.message,
    required this.transaction,
  });
  final bool success;
  final int statusCode;
  final String message;
  final ConfirmTransferDetail transaction;

  @override
  List<Object?> get props => [success, statusCode, message, transaction];
}

class ConfirmTransferDetail extends Equatable {
  const ConfirmTransferDetail({
    required this.id,
    required this.transactionType,
    required this.billAmount,
    required this.originalCurrency,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.paidAmount,
    required this.billRefNo,
    required this.authorizationType,
    required this.status,
    required this.createdAt,
    required this.lastModified,
    this.acceptedAmount,
    this.senderId,
    this.senderName,
    this.senderPhone,
    this.senderEmail,
    this.beneficiaryId,
    this.beneficiaryName,
    this.beneficiaryPhone,
    this.beneficiaryEmail,
    this.beneficiaryAccountNo,
    this.bankName,
    this.bankCode,
    this.bankId,
    this.bankLogo,
    this.amountInEtb,
    this.cardHoderName,
    this.cardNumber,
    this.changedCurrency,
    this.exchangeRate,
    this.paidDate,
    this.ftNumber,
    this.walletFTNumber,
    this.merchantId,
    this.merchantType,
    this.invoiceURL,
    this.billReason,
    this.mpgsRef,
    this.bankRef,
    this.connectRefNo,
    this.ftReference,
    this.senderAccountNo
  });

  final String id;
  final String? senderId;
  final String? senderName;
  final String? senderPhone;
  final String? senderEmail;
  final String? beneficiaryId;
  final String? beneficiaryName;
  final String? beneficiaryPhone;
  final String? beneficiaryEmail;
  final String? beneficiaryAccountNo;
  final String? cardHoderName;
  final String? cardNumber;

  final String? bankName;
  final String? bankCode;
  final String? bankId;
  final String? bankLogo;
  final TransactionType transactionType;
  final double billAmount;

  final double? acceptedAmount;

  final String originalCurrency;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final double? amountInEtb;

  final String? changedCurrency;
  final double? exchangeRate;
  final double paidAmount;
  final String billRefNo;
  final String? paidDate;
  final String? ftNumber;
  final String? walletFTNumber;
  final String? mpgsRef;
  final String? bankRef;

  final String authorizationType;
  final String status;
  final DateTime createdAt;
  final DateTime lastModified;

  // Fields that might be specific to certain transaction types
  final String? merchantId;
  final String? merchantType;
  final String? invoiceURL;
  final String? billReason;
  final String? connectRefNo;
  final String? ftReference;

  final String? senderAccountNo;

  @override
  List<Object?> get props => [
        id,
        senderId,
        senderName,
        senderPhone,
        senderEmail,
        beneficiaryId,
        beneficiaryName,
        beneficiaryPhone,
        beneficiaryEmail,
        beneficiaryAccountNo,
        bankName,
        bankCode,
        bankId,
        bankLogo,
        transactionType,
        billAmount,
        acceptedAmount,
        originalCurrency,
        serviceCharge,
        vat,
        totalAmount,
        changedCurrency,
        exchangeRate,
        paidAmount,
        billRefNo,
        paidDate,
        ftNumber,
        walletFTNumber,
        authorizationType,
        status,
        createdAt,
        lastModified,
        merchantId,
        merchantType,
        invoiceURL,
        billReason,
        senderAccountNo
      ];
}
