import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/custom_number_keyboard.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_wallet_balance.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/transfer_limit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class QrCurrencyInputWidget extends StatefulWidget {
  const QrCurrencyInputWidget({
    required this.controller,
    required this.title,
    required this.subtitle,
    required this.onContinue,
    required this.merchantName,
    required this.merchantId,
    super.key,
    this.balanceLabel,
    this.showExchangeAmount = false,
    this.isLoading = false,
    this.alwaysShowETBColor = false,
    this.buttonText = 'Continue',
    this.transactionType,
    this.merchantTill,
    this.isAmountFromQr = false,
  });
  final CurrencyInputController controller;
  final String title;

  final String subtitle;
  final String? balanceLabel;
  final String? transactionType;
  final bool showExchangeAmount;
  final VoidCallback onContinue;
  final bool isLoading;
  final bool alwaysShowETBColor;
  final String buttonText;
  final String merchantName;
  final String merchantId;
  final String? merchantTill;
  final bool isAmountFromQr;

  @override
  State<QrCurrencyInputWidget> createState() => _QrCurrencyInputWidgetState();
}

class _QrCurrencyInputWidgetState extends State<QrCurrencyInputWidget> {
  final bool _isProcessing = false;
  TransferLimit? _transferLimit;

  @override
  void initState() {
    super.initState();
    _fetchTransferLimits();
  }

  void _fetchTransferLimits() {
    if (widget.transactionType != null) {
      context.read<TransactionBloc>().add(
            FetchTransferLimitEvent(
              transactionType: widget.transactionType!,
              currency: widget.controller.currencyType == CurrencyType.usd
                  ? 'USD'
                  : 'ETB',
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != '') _buildHeader(),
        SizedBox(height: 16.h),
        if (widget.isAmountFromQr)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Text(
                //   'Merchant Info',
                //   style: GoogleFonts.outfit(
                //     fontSize: 14.sp,
                //     color: Colors.black.withValues(alpha: 0.4),
                //     fontWeight: FontWeight.w600,
                //   ),
                // ),
                // SizedBox(height: 16.h),
                Container(
                  padding: EdgeInsets.all(16.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          // Merchant Logo
                          Container(
                            padding: EdgeInsets.all(12.h),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LightModeTheme().primaryGradient,
                            ),
                            child: Center(
                              // backgroundColor: theme.primaryColor,
                              // radius: 18,
                              child: Text(
                                widget.merchantName
                                    .split(' ')
                                    .map(
                                      (e) => e.isNotEmpty
                                          ? e[0].toUpperCase()
                                          : '',
                                    )
                                    .take(2)
                                    .join(),
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Scanned Amount',
                                style: GoogleFonts.outfit(
                                  fontSize: 14.sp,
                                  color: const Color(0xff9A9A9A),
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Text(
                                '${widget.controller.cleanAmount} ETB',
                                style: GoogleFonts.outfit(
                                  fontSize: 24.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Merchant Info',
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          color: Colors.black.withValues(alpha: 0.4),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 12.h,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          image: const DecorationImage(
                            image: AssetImage(MediaRes.birrRecipentBgColor),
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.merchantName,
                              style: GoogleFonts.outfit(
                                fontSize: 16.sp,
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  'Merchant Id: ',
                                  style: GoogleFonts.outfit(
                                    fontSize: 14.sp,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                Text(
                                  '${widget.merchantTill}',
                                  style: GoogleFonts.outfit(
                                    fontSize: 14.sp,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Text(
                      //       'Merchant Name:',
                      //       style: GoogleFonts.outfit(
                      //         fontSize: 14.sp,
                      //         color: Colors.black.withValues(alpha: 0.6),
                      //         fontWeight: FontWeight.w400,
                      //       ),
                      //     ),
                      //     Text(
                      //       widget.merchantName,
                      //       style: GoogleFonts.outfit(
                      //         fontSize: 14.sp,
                      //         color: Colors.black,
                      //         fontWeight: FontWeight.w600,
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      // const SizedBox(height: 10),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Text(
                      //       'Merchant Id:',
                      //       style: GoogleFonts.outfit(
                      //         fontSize: 14.sp,
                      //         color: Colors.black.withValues(alpha: 0.6),
                      //         fontWeight: FontWeight.w400,
                      //       ),
                      //     ),
                      //     Text(
                      //       widget.merchantTill!,
                      //       style: GoogleFonts.outfit(
                      //         fontSize: 14.sp,
                      //         color: Colors.black,
                      //         fontWeight: FontWeight.w600,
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      if (widget.isAmountFromQr)
                        Container(
                          alignment: Alignment.centerLeft,
                          padding: const EdgeInsets.only(top: 15),
                          child: _buildWalletBalance(),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          )
        else
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: RecipientCard(
              alwaysShowETBColor: widget.alwaysShowETBColor,
              name: widget.merchantName,
              accountNumber: widget.merchantTill != null
                  ? 'Merchant ID: ${widget.merchantTill}'
                  : 'Merchant ID: ${widget.merchantId}',
              isBirrTransfer: true,
            ),
          ),
        if (!widget.isAmountFromQr)
          Expanded(
            child: _buildAmountInputSection(),
          ),
        _buildKeypadAndButton(),
      ],
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      child: CustomPageHeader(
        pageTitle: widget.title,
        description: widget.subtitle,
      ),
    );
  }

  Widget _buildAmountInputSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (widget.showExchangeAmount) _buildExchangeAmount(),
                SizedBox(height: 16.h),
                _buildAmountTextField(),
                _buildMinimumAmountIndicator(context),
                ValueListenableBuilder<bool>(
                  valueListenable: widget.controller.exceedsBalanceNotifier,
                  builder: (context, exceedsBalance, _) {
                    return exceedsBalance
                        ? Padding(
                            padding: EdgeInsets.only(top: 8.h),
                            child: Text(
                              'Amount exceeds wallet balance',
                              style: GoogleFonts.outfit(
                                fontSize: 14.sp,
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          )
                        : const SizedBox.shrink();
                  },
                ),
                ValueListenableBuilder<bool>(
                  valueListenable:
                      widget.controller.exceedsTransferLimitNotifier,
                  builder: (context, exceedsLimit, _) {
                    return exceedsLimit
                        ? Padding(
                            padding: EdgeInsets.only(top: 8.h),
                            child: Text(
                              'Amount exceeds maximum transfer limit',
                              style: GoogleFonts.outfit(
                                fontSize: 14.sp,
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          )
                        : const SizedBox.shrink();
                  },
                ),
                SizedBox(height: 16.h),
                if (widget.balanceLabel != null && !widget.isAmountFromQr)
                  _buildWalletBalance(),
                SizedBox(height: 32.h),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWalletBalance() {
    return CustomWalletBalance(
      alwaysShowETBColor: widget.alwaysShowETBColor,
      // walletBalance: 0,
      // formattedBalance: widget.balanceLabel?.split(' ').first ?? '',
      // isBirrBalance: widget.controller.currencyType == CurrencyType.etb
    );
  }

  Widget _buildMinimumAmountIndicator(BuildContext context) {
    return BlocBuilder<TransactionBloc, TransactionState>(
      builder: (context, state) {
        var minAmount = '';
        var maxAmount = '';

        if (state is TransferLimitLoaded) {
          _transferLimit = state.transferLimit;

          // Store limit values in controller
          final minLimit = state.transferLimit.minTransferLimit;
          final maxLimit = state.transferLimit.maxTransferLimit;

          // Set controller's limit properties
          widget.controller.minTransferLimit = minLimit;
          widget.controller.maxTransferLimit = maxLimit;

          // Get current amount for validation
          final currentAmount = widget.controller.numericAmount;

          // Update controller's notifier for max limit
          widget.controller.exceedsTransferLimitNotifier.value =
              currentAmount > maxLimit;

          // The isValidNotifier will be updated in the controller's _handleTextChange method
          // Force a text change event to update validation status
          widget.controller.textController.notifyListeners();

          minAmount = widget.controller.currencyType == CurrencyType.usd
              ? '\$${state.transferLimit.minTransferLimit}'
              : '${state.transferLimit.minTransferLimit} ETB';
          maxAmount = widget.controller.currencyType == CurrencyType.usd
              ? '\$${state.transferLimit.maxTransferLimit}'
              : '${state.transferLimit.maxTransferLimit} ETB';
        }

        return minAmount != ''
            ? Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.black.withOpacity(0.4),
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Minimum Amount:',
                        style: GoogleFonts.outfit(
                          color: Colors.black.withOpacity(0.4),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        ' $minAmount ',
                        style: GoogleFonts.outfit(
                          color: Colors.black.withOpacity(0.4),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildAmountTextField() {
    return TextField(
      controller: widget.controller.textController,
      readOnly: true,
      textAlign: TextAlign.center,
      style: GoogleFonts.outfit(
        fontSize: 40.sp,
        fontWeight: FontWeight.w800,
        color: Colors.black,
      ),
      decoration: InputDecoration(
        border: InputBorder.none,
        hintText: widget.controller.currencyType == CurrencyType.usd
            ? r'$0.00'
            : '0.00 ETB',
        hintStyle: GoogleFonts.outfit(
          fontSize: 40.sp,
          color: Colors.grey,
          fontWeight: FontWeight.w800,
        ),
        // suffix: widget.isAmountFromQr
        //     ? Icon(
        //         Icons.lock,
        //         color: Theme.of(context).primaryColor,
        //         size: 18.sp,
        //       )
        //     : null,
      ),
    );
  }

  Widget _buildExchangeAmount() {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.controller.showSecondaryAmountNotifier,
      builder: (context, showSecondary, _) {
        if (!showSecondary) return const SizedBox.shrink();

        return Container(
          padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 16.w),
          child: Column(
            children: [
              Text(
                widget.controller.currencyType == CurrencyType.usd
                    ? 'Amount In ETB'
                    : 'Amount In USD',
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  color: Colors.black54,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8.h),
              ValueListenableBuilder<String>(
                valueListenable: widget.controller.secondaryAmountNotifier,
                builder: (context, secondaryAmount, _) {
                  return Text(
                    _formatSecondaryAmount(secondaryAmount),
                    style: GoogleFonts.outfit(
                      fontSize: 28.sp,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                      letterSpacing: 0.5,
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatSecondaryAmount(String secondaryAmount) {
    if (secondaryAmount.isEmpty) {
      return widget.controller.currencyType == CurrencyType.usd
          ? '0.00 ETB'
          : r'$0.00';
    }

    try {
      // Extract numeric value from the string (removing currency symbols)
      final regExp = RegExp('[0-9,.]+');
      final match = regExp.firstMatch(secondaryAmount);
      if (match != null) {
        final numStr = match.group(0) ?? '';
        final amount = double.parse(numStr.replaceAll(',', ''));

        // Format with proper thousand separators
        final formattedAmount = _formatWithCommas(amount.toStringAsFixed(2));

        // Add currency symbol in the correct position
        return widget.controller.currencyType == CurrencyType.usd
            ? '$formattedAmount ETB'
            : '\$$formattedAmount';
      }
    } catch (e) {
      // If parsing fails, return the original string
    }

    return secondaryAmount;
  }

  String _formatWithCommas(String number) {
    if (number.contains('.')) {
      final parts = number.split('.');
      return '${_insertCommasIntoWholePart(parts[0])}.${parts[1]}';
    }
    return _insertCommasIntoWholePart(number);
  }

  String _insertCommasIntoWholePart(String wholePart) {
    final characters = wholePart.split('').reversed.toList();
    final withCommas = <String>[];

    for (var i = 0; i < characters.length; i++) {
      if (i > 0 && i % 3 == 0) {
        withCommas.add(',');
      }
      withCommas.add(characters[i]);
    }

    return withCommas.reversed.join();
  }

  Widget _buildKeypadAndButton() {
    // Always show the keyboard, but handle QR amount differently
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: const Color(0xFFF9F9F9),
      ),
      child: Column(
        children: [
          // if (widget.isAmountFromQr)
          //   Padding(
          //     padding: EdgeInsets.only(bottom: 8.h),
          //     child: Text(
          //       'The amount is set from the merchant QR code and cannot be modified',
          //       textAlign: TextAlign.center,
          //       style: GoogleFonts.outfit(
          //         fontSize: 14.sp,
          //         color: Theme.of(context).primaryColor,
          //         fontWeight: FontWeight.w500,
          //       ),
          //     ),
          //   ),
          if (!widget.isAmountFromQr)
            CustomNumberKeyboard(
              onKeyPressed: (key) {
                if (widget.isAmountFromQr) {
                  // Show toast instead of modifying the amount
                  CustomToastification(
                    context,
                    message:
                        'The amount is set from the merchant QR code and cannot be modified',
                    isError: false,
                    successTitle: 'Fixed Amount',
                  );
                } else {
                  // Normal behavior for manually entered amounts
                  widget.controller.onKeyPressed(key);
                }
              },
              useBackspace: false,
              fontSize: 24.sp,
            ),
          SizedBox(height: 12.h),
          if (widget.isAmountFromQr)
            SizedBox(
              height: (MediaQuery.sizeOf(context).height * 0.42).h,
            ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: ValueListenableBuilder<bool>(
              valueListenable: widget.controller.isValidNotifier,
              builder: (context, isValid, _) {
                return SafeArea(
                  child: CustomRoundedBtn(
                    btnText:
                        widget.isLoading ? 'Processing...' : widget.buttonText,
                    onTap: (isValid && !widget.isLoading)
                        ? widget.onContinue
                        : null,
                    isLoading: widget.isLoading,
                    bgColor: (isValid && !widget.isLoading)
                        ? LightModeTheme().primaryColorBirr
                        : Colors.grey,
                    isBtnActive: isValid && !widget.isLoading,
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }
}
