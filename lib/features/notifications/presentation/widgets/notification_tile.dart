import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationTile extends StatelessWidget {
  const NotificationTile({
    required this.description,
    required this.date,
    required this.onTap,
    required this.subject,
    required this.transactionType,
    super.key,
    this.isRead = false,
    this.onDismiss,
  });
  final String description;
  final String subject;
  final String transactionType;
  final String date;
  final VoidCallback onTap;
  final bool isRead;
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    final notificationIcon = getTransactionTypeIcon(transactionType);
    return Column(
      children: [
        if (!isRead)
          Dismissible(
            key: UniqueKey(),
            direction: DismissDirection.endToStart,
            confirmDismiss: (direction) async {
              if (direction == DismissDirection.endToStart && !isRead) {
                onDismiss?.call();
                return false;
              }
              return false;
            },
            background: Container(
              color: Theme.of(context).secondaryHeaderColor,

              // const Color(0xFF1AD9B8).withOpacity(0.1),
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Mark as read $isRead',
                    style: GoogleFonts.outfit(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 14.sp,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.check_circle_outline,
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
            child: InkWell(
              onTap: onTap,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                color: Colors.white,
                constraints: BoxConstraints(
                  minHeight: 88.h,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40.h,
                      height: 40.h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).secondaryHeaderColor,

                        // color: const Color(0xFF1AD9B8).withOpacity(0.1),
                      ),
                      child: Center(
                        child: Image.asset(
                          notificationIcon,
                          // 'assets/images/notification_icon.png',
                          width: 24.w,
                          height: 24.h,

                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.h),
                    Expanded(
                      child: _buildNotificationDetails(),
                    ),
                    if (!isRead) ...[
                      SizedBox(width: 8.h),
                      Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          )
        else
          InkWell(
            onTap: onTap,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              color: Colors.white,
              constraints: BoxConstraints(
                minHeight: 88.h,
              ),
              child: Row(
                children: [
                  Container(
                    width: 40.h,
                    height: 40.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(context).secondaryHeaderColor,

                      // color: const Color(0xFF1AD9B8).withOpacity(0.1),
                    ),
                    child: Center(
                      child: Image.asset(
                        notificationIcon,
                        // 'assets/images/notification_icon.png',
                        width: 24.w,
                        height: 24.h,

                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.h),
                  Expanded(
                    child: _buildNotificationDetails(),
                  ),
                  if (!isRead) ...[
                    SizedBox(width: 8.h),
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: CustomPaint(
            painter: DottedLinePainter(
              color: Colors.grey[300],
            ),
            child: const SizedBox(
              height: 1,
              width: double.infinity,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationDetails() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          subject,
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            color: isRead ? Colors.grey[700] : Colors.black,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 4.h),
        // Message body with proper indentation
        Padding(
          padding: const EdgeInsets.only(left: 2),
          child: Text(
            description
                .split('\n')
                .map((line) => line.trim())
                .where((line) => line.isNotEmpty)
                .join('\n'),
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              color: isRead ? Colors.grey[500] : Colors.black,
              fontWeight: isRead ? FontWeight.normal : FontWeight.w400,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        SizedBox(height: 8.h),
        // Date
        Text(
          date,
          style: GoogleFonts.outfit(
            fontSize: 14.sp,
            letterSpacing: 0.1,
            color: const Color(0xFFAAAAAA),
          ),
        ),
      ],
    );
  }

  String getTransactionTypeIcon(String transactionType) {
    debugPrint(transactionType);
    switch (transactionType) {
      case 'bank_transfer':
        return MediaRes.bankIcon;
      case 'wallet_transfer':
        return MediaRes.walletTransferIcon;
      case 'cash_pickup':
        return MediaRes.ordersIcon;
      case 'top_up':
        return MediaRes.mobileTopUpIcon;

      case 'wallet_prefund':
        return MediaRes.walletTransferIcon;
      case 'load_to_wallet':
        return MediaRes.loadToWalletIcon;
      case 'add_money':
        return MediaRes.addMoneyIcon;

      case 'change_to_birr':
        return MediaRes.changeToBirrIcon;

      case 'guest_send_money':
        return MediaRes.walletTransferIcon;

      case 'gift_package':
        return MediaRes.sentGiftCardIcon;

      case 'redeem':
        return MediaRes.sentGiftCardIcon;

      case 'cash_in':
      case 'cash_out':
        return MediaRes.usdCashIn;

      default:
        return MediaRes.changeToBirrIcon;
    }
  }
}
