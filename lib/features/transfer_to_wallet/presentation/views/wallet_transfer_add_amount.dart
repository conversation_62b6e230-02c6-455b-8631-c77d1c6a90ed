import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/features/chat/presentation/managers/chat_money_manager.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_bloc.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_event.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_state.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_recipients_bottom_sheet.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/quick_pay_recipent_card.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class WalletTransferAddAmount extends StatefulWidget {
  const WalletTransferAddAmount({
    required this.currencyType,
    this.memberInfo,
    this.recipent,
    this.isFromQuick = true,
    this.ignoreAmountCheck = false,
    this.isFromChat = false,
    this.chatContext,
    super.key,
  });
  final MemberLookupResponse? memberInfo;
  final RecentRecipient? recipent;

  final CurrencyType currencyType;
  final bool isFromQuick;
  final bool ignoreAmountCheck;
  final bool isFromChat;
  final Map<String, dynamic>? chatContext;
  @override
  State<WalletTransferAddAmount> createState() =>
      _WalletTransferAddAmountState();
}

class _WalletTransferAddAmountState extends State<WalletTransferAddAmount> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;
  double dasheExchangeRate = 0;

  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;
  ConfirmTransferResponse? _lastTransactionResponse;

  @override
  void initState() {
    super.initState();

    final exchangeRateState = context.read<ExchangeRateBloc>().state;

    if (exchangeRateState is ExchangeRateLoaded) {
      setState(() {
        dasheExchangeRate = exchangeRateState.rates.rate;
      });
    } else {
      context.read<ExchangeRateBloc>().add(FetchExchangeRates());
    }

    _currencyController = CurrencyInputController(
      currencyType: (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: 0,
      ignoreWalletAmountCheck: widget.ignoreAmountCheck,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      isFromChat: widget.isFromChat,
      transactionType: tx_type.TransactionType.walletTransfer,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<WalletTransferBloc>().state
                        as WalletTransferPinRequired)
                    .billRefNo,
                transactionType: tx_type.TransactionType.walletTransfer,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        if (!widget.isFromChat) {
          final recentTransaction = RecentWalletTransferHive(
            recipientId: response.transaction.beneficiaryId ?? '',
            recipientEmail: response.transaction.beneficiaryEmail ?? '',
            recipientPhone: response.transaction.beneficiaryPhone ?? '',
            recipientName: response.transaction.beneficiaryName ?? '',
            createdAt: DateTime.now(),
            avatar:
                widget.recipent?.avatarUrl ?? widget.memberInfo?.avatar ?? '',
          );

          context.read<RecentWalletTransferBloc>().add(
                SaveRecentWalletTransferEvent(
                  transaction: recentTransaction,
                ),
              );
        }
        Navigator.pop(context);

        if (widget.isFromChat && widget.chatContext != null) {
          _handleChatTransferSuccess(response);
        } else {
          _showSuccessScreenBottomSheet(response);
        }
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        if (widget.isFromChat && widget.chatContext != null) {
          _navigateBackToChat();
        } else {
          context.go(AppRouteName.home);
        }
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }
      setState(() {
        _isLoading = true;
      });

      _checkTransferRule();
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  Future<void> _checkTransferRule() async {
    final amount = _currencyController.numericAmount;

    context.read<CheckRuleTransferBloc>().add(
          CheckWalletTransferRulesRequested(
            amount: amount,
            currency: _currencyController.currencyType == CurrencyType.usd
                ? 'USD'
                : 'ETB',
            productType: 'wallet_transfer',
          ),
        );
  }

  void _showConfirmScreenBottomSheet(WalletTransferResponse response) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Wallet to Wallet Transfer',
        'Sender Name': response.data.senderName,
        'Sender Account':
            response.data.senderPhone ?? response.data.senderEmail,

        'Recipient Name': response.data.beneficiaryName,
        if (response.data.beneficiaryEmail?.isNotEmpty ?? false)
          'Recipient Email': response.data.beneficiaryEmail,
        if (response.data.beneficiaryPhone?.isNotEmpty ?? false)
          'Recipient Phone': response.data.beneficiaryPhone,

        if (response.data.originalCurrency == 'USD')
          'Amount in USD':
              '${response.data.billAmount} ${response.data.originalCurrency}',

        if (response.data.originalCurrency == 'USD')
          'Exchange Rate': '1\$ = ${response.data.exchangeRate} ETB',

        if (response.data.originalCurrency == 'USD')
          'Amount in ETB': '${response.data.paidAmount} ETB',
        if (response.data.originalCurrency == 'ETB')
          'Amount': '${response.data.billAmount} ETB',
        'Service Charge':
            '${response.data.serviceCharge} ${response.data.originalCurrency}',

        'VAT': '${response.data.vat} ${response.data.originalCurrency}',

        'Date': AppMapper.safeFormattedDate(response.data.createdAt),
        // 'BillRef No': response.data.billRefNo,
      },
      originalCurrency: response.data.originalCurrency,
      totalAmount: response.data.totalAmount,
      billAmount: response.data.billAmount,
      requiresOtp:
          WalletTransferAuthResponse.fromJson(response.toJson()).requiresOtp,
      billRefNo: response.data.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
        totalAmount: transaction.totalAmount,
        billAmount: transaction.billAmount,
        originalCurrency: transaction.originalCurrency,
        transactionId: transaction.id,
        billRefNo: transaction.billRefNo,
        status: 'Paid',
        title: 'Your wallet to wallet transfer was completed successfully',
        {
          'Transaction Type': 'Wallet to Wallet Transfer',
          'Sender Name': transaction.senderName,
          'Sender Account': transaction.senderPhone ?? transaction.senderEmail,

          'Recipient Name': transaction.beneficiaryName,
          if (transaction.beneficiaryEmail?.isNotEmpty ?? false)
            'Recipient Email': transaction.beneficiaryEmail,
          if (transaction.beneficiaryPhone?.isNotEmpty ?? false)
            'Recipient Phone': transaction.beneficiaryPhone,

          if (transaction.originalCurrency == 'USD')
            'Amount In USD': '${transaction.billAmount} USD',

          if (transaction.originalCurrency == 'USD')
            'Exchange Rate': '1\$ = ${transaction.exchangeRate} ETB',

          if (transaction.originalCurrency == 'USD')
            'Amount In ETB': '${transaction.paidAmount} ETB'
          else
            'Amount In ETB': '${transaction.billAmount} ETB',

          //widget.memberInfo.phoneNumber,

          'Service Charge':
              '${transaction.serviceCharge} ${transaction.originalCurrency}',
          'VAT': '${transaction.vat} ${transaction.originalCurrency}',

          'Date': AppMapper.safeFormattedDate(transaction.createdAt),
          'Connect Ref No': transaction.walletFTNumber,
        });
  }

  @override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transfer To Wallet'),
      ),
      body: SafeArea(
        child: MultiBlocListener(
          listeners: [
            BlocListener<TransactionBloc, TransactionState>(
              listenWhen: (previous, current) =>
                  current is ConfirmTransferSuccess ||
                  current is ConfirmTransferError,
              listener: (context, state) {
                if (state is ConfirmTransferError) {
                  CustomToastification(context, message: state.message);
                }
              },
            ),
            BlocListener<WalletTransferBloc, WalletTransferState>(
              listenWhen: (previous, current) =>
                  current is CheckingWalletTransferRules ||
                  current is WalletTransferRulesChecked ||
                  current is WalletTransferLoading ||
                  current is WalletTransferPinRequired ||
                  current is WalletTransferFailure ||
                  current is WalletDetailsLoaded ||
                  current is WalletTransferError,
              listener: (context, state) {
                if (state is CheckingWalletTransferRules ||
                    state is WalletTransferLoading) {
                  setState(() => _isLoading = true);
                } else if (state is WalletTransferRulesChecked) {
                  setState(() => _isLoading = false);
                  context.read<WalletTransferBloc>().add(
                        TransferToWalletEvent(
                          beneficiaryEmail: widget.isFromQuick
                              ? widget.recipent?.email
                              : widget.memberInfo?.email,
                          beneficiaryPhone: widget.isFromQuick
                              ? widget.recipent?.phone
                              : widget.memberInfo?.phoneNumber,
                          beneficiaryId: widget.isFromQuick
                              ? widget.recipent?.id
                              : widget.memberInfo?.id,
                          amount: _currencyController.numericAmount,
                          currency: _currencyController.currencyType ==
                                  CurrencyType.usd
                              ? 'USD'
                              : 'ETB',
                        ),
                      );
                } else if (state is WalletTransferPinRequired) {
                  setState(() => _isLoading = false);
                  _showConfirmScreenBottomSheet(state.response);
                } else if (state is WalletTransferFailure ||
                    state is WalletTransferError) {
                  setState(() => _isLoading = false);
                  CustomToastification(
                    context,
                    message: state is WalletTransferFailure
                        ? state.message
                        : (state as WalletTransferError).message,
                  );
                }
              },
            ),
          ],
          child: BlocBuilder<WalletTransferBloc, WalletTransferState>(
            buildWhen: (previous, current) =>
                current is WalletTransferInitial ||
                current is WalletTransferLoading ||
                current is WalletDetailsLoaded,
            builder: (context, state) {
              if (state is WalletDetailsLoadingState) {
                return const CustomConnectLoader();
              }

              return BlocListener<CheckRuleTransferBloc,
                  CheckRuleTransferState>(
                listener: (context, state) {
                  if (state is CheckingWalletTransferRules ||
                      state is WalletTransferLoading) {
                    setState(() => _isLoading = true);
                  }

                  if (state is CheckTransferRulesChecked) {
                    setState(() => _isLoading = false);
                    context.read<WalletTransferBloc>().add(
                          TransferToWalletEvent(
                            beneficiaryEmail: widget.isFromQuick
                                ? widget.recipent?.email
                                : widget.memberInfo?.email,
                            beneficiaryPhone: widget.isFromQuick
                                ? widget.recipent?.phone
                                : widget.memberInfo?.phoneNumber,
                            beneficiaryId: widget.isFromQuick
                                ? widget.recipent?.id
                                : widget.memberInfo?.id,
                            amount: _currencyController.numericAmount,
                            currency: _currencyController.currencyType ==
                                    CurrencyType.usd
                                ? 'USD'
                                : 'ETB',
                          ),
                        );
                  } else if (state is CheckTransferRulesFailure) {
                    setState(() {
                      _isLoading = false;
                    });
                    CustomToastification(context, message: state.message);
                  }
                },
                child: CurrencyInputWidget(
                  controller: _currencyController,
                  title: 'Add Amount',
                  header: !widget.isFromQuick
                      ? null
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 2),
                            const CustomPagePadding(
                              padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
                              child: CustomPageHeader(
                                pageTitle: 'Add Amount ',
                                description:
                                    'Enter the amount you wish to send to the recipient and submit.',
                              ),
                            ),
                            const SizedBox(height: 4),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: QuickPayRecipentCard(
                                recipientName:
                                    widget.recipent?.name ?? 'rober insarmu',
                                recipientPhone: widget.recipent?.phone ?? '',
                                recipientAvatar:
                                    widget.recipent?.avatarUrl ?? '',
                              ),
                            ),
                          ],
                        ),
                  transactionType: 'wallet_transfer',
                  subtitle:
                      'Enter the amount you wish to send to recipient and submit.',
                  hasCustomWalletDisplay: true,
                  showExchangeAmount: true,
                  showBalanceSwitching: true,
                  exchangeAmount: dasheExchangeRate,
                  resetBalance: (selectedWallet) {
                    setState(() {
                      _currencyController = CurrencyInputController(
                        currencyType: selectedWallet == 'USD'
                            ? CurrencyType.usd
                            : CurrencyType.etb,
                        maxBalance: 0,
                      );
                    });
                  },
                  onContinue: _onContinuePressed,
                  isLoading: _isLoading,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// Handle successful transfer when coming from chat
  void _handleChatTransferSuccess(ConfirmTransferResponse response) {
    // Store the transaction response for later use
    _lastTransactionResponse = response;

    // Show success screen with custom callback
    _showSuccessScreenBottomSheet(response);
  }

  /// Navigate back to chat conversation and send success message
  void _navigateBackToChat() {
    final chatContext = widget.chatContext!;
    final conversationId = chatContext['conversationId'] as String;
    final receiverName = chatContext['receiverName'] as String;

    // Send success message to chat before navigating
    if (_lastTransactionResponse != null) {
      ChatMoneyManager.sendMoneySuccessMessageToChat(
        context: context,
        conversationId: conversationId,
        receiverName: receiverName,
        amount: _lastTransactionResponse!.transaction.billAmount,
        currency: _lastTransactionResponse!.transaction.originalCurrency,
      );
    }

    // Navigate back to chat conversation
    print(
      '🔥🔥🔥 Regular wallet: Navigating to chat interface: $conversationId',
    );
    context.go('/chat-interface/$conversationId');
  }
}
