part of 'wallet_transfer_bloc.dart';

abstract class WalletTransferState extends Equatable {
  const WalletTransferState();

  @override
  List<Object?> get props => [];
}

class WalletTransferInitial extends WalletTransferState {
  const WalletTransferInitial();
}

class WalletTransferLoading extends WalletTransferState {
  const WalletTransferLoading();
}

class QuickWalletLoading extends WalletTransferState {
  const QuickWalletLoading();
}

class WalletTransferPinFailureState extends WalletTransferState {
  const WalletTransferPinFailureState({required this.message});
  final String message;

  @override
  List<Object?> get props => [message];
}

class WalletTransferPinRequired extends WalletTransferState {
  const WalletTransferPinRequired({
    required this.billRefNo,
    required this.requiresOtp,
    required this.response,
    this.otp,
    this.isOtpVerified = false,
  });
  final String billRefNo;
  final bool requiresOtp;
  final WalletTransferResponse response;
  final String? otp;
  final bool isOtpVerified;

  @override
  List<Object?> get props => [billRefNo, requiresOtp, otp, isOtpVerified];
}

class WalletTransferSuccess extends WalletTransferState {
  const WalletTransferSuccess({
    required this.message,
    required this.response,
    required this.shouldNavigate,
    required this.recipientName,
    required this.amount,
    required this.transferNumber,
  });
  final String message;
  final WalletTransferResponse response;
  final bool shouldNavigate;
  final String recipientName;
  final String amount;
  final String transferNumber;

  @override
  List<Object?> get props => [
        message,
        response,
        shouldNavigate,
        recipientName,
        amount,
        transferNumber,
      ];
}

class WalletTransferError extends WalletTransferState {
  const WalletTransferError(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

class ContactsLookupSuccess extends WalletTransferState {
  const ContactsLookupSuccess(this.contacts);
  final List<ContactLookupResponse> contacts;

  @override
  List<Object?> get props => [contacts];
}

class MemberLookupSuccess extends WalletTransferState {
  const MemberLookupSuccess(this.member);
  final MemberLookupResponse member;

  @override
  List<Object?> get props => [member];
}

class TransferAmountValidated extends WalletTransferState {
  const TransferAmountValidated();
}

class TransferStatusChecked extends WalletTransferState {
  const TransferStatusChecked(this.status);
  final TransferStatus status;

  @override
  List<Object?> get props => [status];
}

class WalletDetailsLoadingState extends WalletTransferState {
  const WalletDetailsLoadingState();

  @override
  List<Object?> get props => [];
}

class WalletDetailsLoaded extends WalletTransferState {
  const WalletDetailsLoaded(this.wallet);
  final WalletInfo wallet;

  double get balance => wallet.getBalance(currency);
  String get walletCode => wallet.getWalletCode(currency);
  String get currency => wallet.currency;

  @override
  List<Object> get props => [wallet];
}

class MemberLookupNotFound extends WalletTransferState {
  const MemberLookupNotFound(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

class WalletOtpResendSuccess extends WalletTransferState {
  const WalletOtpResendSuccess({required this.otp});
  final String otp;

  @override
  List<Object> get props => [otp];
}

class WalletTransferOtpVerifiedState extends WalletTransferState {
  const WalletTransferOtpVerifiedState({
    required this.billRefNo,
    required this.response,
  });
  final String billRefNo;
  final WalletTransferResponse response;

  @override
  List<Object> get props => [billRefNo, response];
}



class WalletTransferRulesChecked extends WalletTransferState {
  const WalletTransferRulesChecked(this.rulesResponse);
  final CheckTransferRulesResponse rulesResponse;

  @override
  List<Object?> get props => [rulesResponse];
}

class GeneratedReceiptState extends WalletTransferState {
  const GeneratedReceiptState(this.receiptUrl);
  final String receiptUrl;

  @override
  List<Object?> get props => [receiptUrl];
}

class WalletTransferFailure extends WalletTransferState {
  const WalletTransferFailure(this.message);
  final String message;

  @override
  List<Object> get props => [message];
}

class LoadedRecentWalletTransferState extends WalletTransferState {
  const LoadedRecentWalletTransferState(this.recentWalletTransactions);
  final List<RecentWalletTransferHive> recentWalletTransactions;

  @override
  List<Object?> get props => [recentWalletTransactions];
}

class EmptyRecentWalletTransferState extends WalletTransferState {
  const EmptyRecentWalletTransferState();

  @override
  List<Object?> get props => [];
}
