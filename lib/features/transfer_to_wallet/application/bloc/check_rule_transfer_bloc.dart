import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/check_transfer_rules.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// EVENT
abstract class CheckRuleTransferEvent extends Equatable {
  const CheckRuleTransferEvent();

  @override
  List<Object?> get props => [];
}

class CheckWalletTransferRulesRequested extends CheckRuleTransferEvent {
  const CheckWalletTransferRulesRequested({
    required this.amount,
    required this.currency,
    required this.productType,
    this.accountNumber,
    this.bankID,
  });
  final double amount;
  final String currency;
  final String productType;
  final String? accountNumber;
  final String? bankID;


  @override
  List<Object?> get props => [amount, currency, productType, accountNumber, bankID];
}

abstract class CheckRuleTransferState extends Equatable {
  const CheckRuleTransferState();

  @override
  List<Object?> get props => [];
}

class CheckRuleTransferInitial extends CheckRuleTransferState {
  const CheckRuleTransferInitial();
}

class CheckingWalletTransferRules extends CheckRuleTransferState {
  const CheckingWalletTransferRules();
  @override
  List<Object?> get props => [];
}

class CheckTransferRulesChecked extends CheckRuleTransferState {
  const CheckTransferRulesChecked(this.response);
  final CheckTransferRulesResponse response;

  @override
  List<Object?> get props => [response];
}

class CheckTransferRulesFailure extends CheckRuleTransferState {
  const CheckTransferRulesFailure(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

class CheckRuleTransferBloc
    extends Bloc<CheckRuleTransferEvent, CheckRuleTransferState> {
  CheckRuleTransferBloc({
    required this.checkTransferRulesUseCase,
  }) : super(const CheckRuleTransferInitial()) {
    on<CheckWalletTransferRulesRequested>(_onCheckTransferRulesRequested);
  }

  final CheckWalletTransferRules checkTransferRulesUseCase;

  Future<void> _onCheckTransferRulesRequested(
    CheckWalletTransferRulesRequested event,
    Emitter<CheckRuleTransferState> emit,
  ) async {
    emit(const CheckingWalletTransferRules());

    final result = await checkTransferRulesUseCase(
      CheckWalletTransferRulesParams(
        amount: event.amount,
        currency: event.currency,
        productType: event.productType,
        accountNumber: event.accountNumber,
        bankID: event.bankID
      ),
    );

    result.fold(
      (failure) => emit(CheckTransferRulesFailure(failure.message)),
      (response) {
        emit(CheckTransferRulesChecked(response));
      },
    );
  }
}
