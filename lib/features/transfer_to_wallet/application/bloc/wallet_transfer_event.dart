part of 'wallet_transfer_bloc.dart';

abstract class WalletTransferEvent extends Equatable {
  const WalletTransferEvent();

  @override
  List<Object?> get props => [];
}

class LookupContactsEvent extends WalletTransferEvent {
  const LookupContactsEvent({required this.phoneNumbers});
  final List<String> phoneNumbers;

  @override
  List<Object?> get props => [phoneNumbers];
}

class LookupMemberEvent extends WalletTransferEvent {
  const LookupMemberEvent({
    this.email,
    this.phoneNumber,
  });
  final String? email;
  final String? phoneNumber;

  @override
  List<Object?> get props => [email, phoneNumber];
}

class TransferToWalletEvent extends WalletTransferEvent {
  const TransferToWalletEvent({
    required this.amount,
    required this.currency,
    this.beneficiaryEmail,
    this.beneficiaryId,
    this.beneficiaryPhone,
  }) : assert(
          beneficiaryEmail != null ||
              beneficiaryPhone != null ||
              beneficiaryId != null,
          'Either beneficiaryEmail or beneficiaryPhone or beneficiaryId must be provided',
        );
  final String? beneficiaryEmail;
  final String? beneficiaryPhone;
  final String? beneficiaryId;
  final double amount;
  final String currency;

  @override
  List<Object?> get props => [
        beneficiaryEmail,
        beneficiaryPhone,
        amount,
        currency,
      ];
}

class ValidateTransferAmountEvent extends WalletTransferEvent {
  const ValidateTransferAmountEvent({
    required this.amount,
    required this.currency,
  });
  final double amount;
  final String currency;

  @override
  List<Object?> get props => [amount, currency];
}

class CheckTransferStatusEvent extends WalletTransferEvent {
  const CheckTransferStatusEvent({required this.transactionId});
  final String transactionId;

  @override
  List<Object?> get props => [transactionId];
}

/*
class GetWalletDetailsEvent extends WalletTransferEvent {
  const GetWalletDetailsEvent({required this.currency});
  final String currency;

  @override
  List<Object?> get props => [currency];
}
*/
class WalletTransferPinSubmitted extends WalletTransferEvent {
  const WalletTransferPinSubmitted({
    required this.pin,
    required this.billRefNo,
    this.otp,
  });
  final String pin;
  final String billRefNo;
  final String? otp;

  @override
  List<Object?> get props => [pin, billRefNo, otp];
}

class ResendWalletOtpRequested extends WalletTransferEvent {
  const ResendWalletOtpRequested({required this.billRefNo});
  final String billRefNo;

  @override
  List<Object> get props => [billRefNo];
}

class WalletTransferOtpVerified extends WalletTransferEvent {
  const WalletTransferOtpVerified({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  @override
  List<Object> get props => [billRefNo, otpFor, otpCode];
}



class GenerateRecieptEvent extends WalletTransferEvent {
  const GenerateRecieptEvent({
    required this.billRefNo,
  });
  final String billRefNo;


  @override
  List<Object?> get props => [billRefNo, ];
}

class SaveRecentWalletTransferEvent extends WalletTransferEvent {
  const SaveRecentWalletTransferEvent({
    required this.transaction,
  });
  final RecentWalletTransferHive transaction;

  @override
  List<Object?> get props => [
        transaction,
      ];
}

class GetRecentWalletTransferEvent extends WalletTransferEvent {
  const GetRecentWalletTransferEvent({required this.limit});
  final int limit;

  @override
  List<Object?> get props => [limit];
}

class UpdateQrEvent extends WalletTransferEvent {
  const UpdateQrEvent({
    required this.qrId,
  });
  final String qrId;


  @override
  List<Object?> get props => [qrId, ];
}