import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/send_money/data/models/transfer_status_model.dart';
import 'package:cbrs/features/transfer_to_wallet/data/datasources/wallet_transfer_remote_datasource.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/otp_resend_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/contact_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/wallet_info.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/repositories/wallet_transfer_repository.dart';
import 'package:dartz/dartz.dart';

class WalletTransferRepositoryImpl implements WalletTransferRepository {
  const WalletTransferRepositoryImpl(this._remoteDataSource);
  final WalletTransferRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<List<ContactLookupResponse>> lookupContacts({
    required List<String> phoneNumbers,
  }) async {
    try {
      final result = await _remoteDataSource.lookupContacts(
        phoneNumbers: phoneNumbers,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<MemberLookupResponse> lookupMember({
    String? email,
    String? phoneNumber,
  }) async {
    try {
      final result = await _remoteDataSource.lookupMember(
        email: email,
        phoneNumber: phoneNumber,
      );
      return Right(result);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.message));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<WalletTransferResponse> transferToWallet({
    required double amount,
    required String currency,
    String? beneficiaryEmail,
    String? beneficiaryPhone,
    String? recipientID,
  }) async {
    try {
      final result = await _remoteDataSource.transferToWallet(
        beneficiaryEmail: beneficiaryEmail,
        beneficiaryPhone: beneficiaryPhone,
        recipientID: recipientID,
        amount: amount,
        currency: currency,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultVoid validateTransferAmount({
    required double amount,
    required String currency,
  }) async {
    try {
      await _remoteDataSource.validateTransferAmount(
        amount: amount,
        currency: currency,
      );
      return const Right(null);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<TransferStatusModel> checkTransferStatus({
    required String transactionId,
  }) async {
    try {
      final status = await _remoteDataSource.checkTransferStatus(
        transactionId: transactionId,
      );
      return Right(status);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.message));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  // @override
  // ResultFuture<WalletInfo> getWalletDetails() async {
  //   try {
  //     final result = await _remoteDataSource.getWalletDetails();
  //     return Right(result);
  //   } on ApiException catch (e) {
  //     return Left(ServerFailure(message: e.message));
  //   }
  // }

  @override
  ResultFuture<WalletTransferResponse> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  }) async {
    try {
      final response = await _remoteDataSource.submitPin(
        pin: pin,
        billRefNo: billRefNo,
        otp: otp,
      );
      return Right(response);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    try {
      final response = await _remoteDataSource.resendOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
      );
      return Right(response);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<WalletTransferResponse> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    try {
      final response = await _remoteDataSource.verifyOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
        otpCode: otpCode,
      );
      return Right(response);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String productType,
    required String? accountNumber,
    required String? bankID,
  }) async {
    try {
      final result = await _remoteDataSource.checkTransferRules(
        amount: amount,
        currency: currency,
        productType: productType,
        accountNumber: accountNumber,
        bankID: bankID

      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<String> generateReceipt({required String billRefNo}) async {
    try {
      final result = await _remoteDataSource.generateReceipt(
        billRefNo: billRefNo,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<Map<String, String>> getUsersAvatar({
    required List<String> userIds,
  }) async {
    try {
      final result = await _remoteDataSource.getUsersAvatar(
        userIds: userIds,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<bool> updateQrStatus({required String qrId}) async {
    try {
      final result = await _remoteDataSource.updateQrStatus(
        qrId: qrId,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }
}
