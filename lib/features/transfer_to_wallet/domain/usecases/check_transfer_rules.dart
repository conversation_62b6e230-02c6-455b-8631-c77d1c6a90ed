import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/repositories/wallet_transfer_repository.dart';
import 'package:equatable/equatable.dart';

class CheckWalletTransferRules extends UsecaseWithParams<
    CheckTransferRulesResponse, CheckWalletTransferRulesParams> {
  const CheckWalletTransferRules(this._repository);
  final WalletTransferRepository _repository;

  @override
  ResultFuture<CheckTransferRulesResponse> call(
    CheckWalletTransferRulesParams params,
  ) async {
    return _repository.checkTransferRules(
      amount: params.amount,
      currency: params.currency,
      productType: params.productType,
      accountNumber: params.accountNumber,
      bankID: params.bankID
    );
  }
}

class CheckWalletTransferRulesParams extends Equatable {
  const CheckWalletTransferRulesParams({
    required this.amount,
    required this.currency,
    required this.productType,
    this.accountNumber,
    this.bankID,
  });
  final double amount;
  final String currency;
  final String productType;
  final String? accountNumber;
  final String? bankID;

  @override
  List<Object?> get props => [amount, currency, productType];
}
