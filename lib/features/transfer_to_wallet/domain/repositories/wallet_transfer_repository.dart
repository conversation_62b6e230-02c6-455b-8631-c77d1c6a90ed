import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/send_money/data/models/transfer_status_model.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/otp_resend_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/contact_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/wallet_info.dart';

abstract class WalletTransferRepository {
  ResultFuture<List<ContactLookupResponse>> lookupContacts({
    required List<String> phoneNumbers,
  });

  ResultFuture<MemberLookupResponse> lookupMember({
    String? email,
    String? phoneNumber,
  });

  ResultFuture<WalletTransferResponse> transferToWallet({
    required double amount,
    required String currency,
    String? beneficiaryEmail,
    String? beneficiaryPhone,
    String? recipientID,
  });

  ResultVoid validateTransferAmount({
    required double amount,
    required String currency,
  });

  ResultFuture<TransferStatusModel> checkTransferStatus({
    required String transactionId,
  });

  // ResultFuture<WalletInfo> getWalletDetails();

  ResultFuture<WalletTransferResponse> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  });

  ResultFuture<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  });

  ResultFuture<WalletTransferResponse> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });

  ResultFuture<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String productType,
  required String? accountNumber,
  required String? bankID,

  });

  ResultFuture<String> generateReceipt({
    required String billRefNo,
  });

  ResultFuture<bool> updateQrStatus({
    required String qrId,
  });

  ResultFuture<Map<String, String>> getUsersAvatar({
    required List<String> userIds,
  });
}
