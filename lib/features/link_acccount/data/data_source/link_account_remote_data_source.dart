import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/link_acccount/data/model/fetch_link_account_model.dart';
import 'package:cbrs/features/link_acccount/data/model/link_account_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

abstract class LinkAccountRemoteDataSource {
  Future<AccountLinkModel> generateLinkCode({
    required String bankID,
    required String accountNumber,
    required String bankHolderName,
  });

  Future<FetchAccountLinkModel> fetchLinkedAccount({
    required int page,
    required int limit,
    String status = 'PENDING',
  });
}

class LinkAccountRemoteDataSourceImpl implements LinkAccountRemoteDataSource {
  LinkAccountRemoteDataSourceImpl({
    required ApiService apiService,
  }) : _apiService = apiService;
  final ApiService _apiService;

  @override
  Future<FetchAccountLinkModel> fetchLinkedAccount({
    required int page,
    required int limit,
    String status = 'PENDING',
  }) async {
    // TODO: implement fetchLinkedAccount

    try {
      debugPrint(
        'Remote fetch Linked account () :: link status $status current page',
      );

      final queryParams = {
        'status': status.toUpperCase(),
      };
      final result = await _apiService.get(
        ApiEndpoints.fetchLinkedAccount,
        queryParameters: queryParams,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((data) {
        final returnData = FetchAccountLinkModel.fromJson(data);

        return returnData;
      }, (err) {
        throw const ApiException(
          message: 'Empty response from server',
          statusCode: 500,
        );
      });
    } on ApiException {
      rethrow;
    } catch (err) {
      throw const ApiException(
        message: 'Error fetching linked accounts',
        statusCode: 500,
      );
    }
  }

  @override
  Future<AccountLinkModel> generateLinkCode({
    required String bankID,
    required String accountNumber,
    required String bankHolderName

  }) async {
    try {
      debugPrint(
        'Remote generate Linked account () :: bankID $bankID accountNumber $accountNumber',
      );

      final data = {
        'bank': bankID,
        'accountNumber': accountNumber,
        'accountHolderName': bankHolderName
      };
      final result = await _apiService.post(
        ApiEndpoints.generateLinkCode,
        data: data,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((data) {
        debugPrint('data $data');
        final returnData = AccountLinkModel.fromJson(data);

        return returnData;
      }, (err) {
        throw ApiException(
          message: err.message,
          statusCode: err.statusCode ?? 400,
        );
      });
    } on ApiException {
      rethrow;
    } catch (err) {
      throw const ApiException(
        message: 'Error in generating linked accounts',
        statusCode: 500,
      );
    }
  }
}
