import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/link_acccount/data/data_source/link_account_remote_data_source.dart';
import 'package:cbrs/features/link_acccount/data/model/fetch_link_account_model.dart';
import 'package:cbrs/features/link_acccount/data/model/link_account_model.dart';
import 'package:cbrs/features/link_acccount/domain/entities/link_account_entities.dart';
import 'package:cbrs/features/link_acccount/domain/repositories/link_account_repositories.dart';
import 'package:dartz/dartz.dart';

class LinkAccountRepositoriesImpl implements LinkAccountRepositories {
  const LinkAccountRepositoriesImpl({
    required LinkAccountRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  final LinkAccountRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<FetchAccountLinkModel> fetchLinkedAccount({
    required int page,
    required int limit,
    String status = 'PENDING',
  }) async {
    try {
      final returnData = await _remoteDataSource.fetchLinkedAccount(
        status: status,
        page: page,
        limit: limit,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<AccountLinkEntity> generateCode({
    required String bankID,
    required String accountNumber,
    required String bankHolderName
  }) async {
    try {
      final returnData = await _remoteDataSource.generateLinkCode(
        bankID: bankID,
        accountNumber: accountNumber,
        bankHolderName: bankHolderName
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }
}
