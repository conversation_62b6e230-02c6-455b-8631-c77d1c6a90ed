import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/link_acccount/domain/entities/fetch_link_account_entity.dart';
import 'package:cbrs/features/link_acccount/domain/entities/link_account_entities.dart';

abstract class LinkAccountRepositories {
  ResultFuture<AccountLinkEntity> generateCode({
    required String bankID,
    required String accountNumber,
    required String bankHolderName,

  });

  ResultFuture<FetchAccountLinkEntity> fetchLinkedAccount({
    required int page,
    required int limit,
    String status = 'PENDING',
  });
}
