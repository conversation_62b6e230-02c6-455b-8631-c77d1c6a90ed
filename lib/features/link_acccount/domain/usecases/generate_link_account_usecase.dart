import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/link_acccount/domain/entities/fetch_link_account_entity.dart';
import 'package:cbrs/features/link_acccount/domain/entities/link_account_entities.dart';
import 'package:cbrs/features/link_acccount/domain/repositories/link_account_repositories.dart';
import 'package:equatable/equatable.dart';

class GenerateLinkAccountUsecase
    extends UsecaseWithParams<void, GenerateCodeParams> {
  GenerateLinkAccountUsecase(this.repository);
  final LinkAccountRepositories repository;

  @override
  ResultFuture<AccountLinkEntity> call(GenerateCodeParams params) {
    return repository.generateCode(
      bankID: params.bankID,
      accountNumber: params.accountNumber,
      bankHolderName: params.bankHolderName
    );
  }
}

class GenerateCodeParams extends Equatable {
  const GenerateCodeParams( {required this.bankID, required this.accountNumber, required this.bankHolderName});
  final String bankID;
  final String accountNumber;
  final String bankHolderName;

  @override
  List<Object?> get props => [bankID, accountNumber];
}
