abstract class AccountLinkEvent {}

class FetchLinkedAccountEvent extends AccountLinkEvent {
  FetchLinkedAccountEvent(
      {this.status = "PENDING", this.limit = 10, this.page = 1});
  final String status;
  final int limit;
  final int page;
}

class GenerateLinkCodeEvent extends AccountLinkEvent {
  GenerateLinkCodeEvent({required this.bankID, required this.accountNumber, required this.bankHolderName});
  final String bankID;
  final String accountNumber;
  final String bankHolderName;
}

class ClearLinkAccountEvent extends AccountLinkEvent {}
