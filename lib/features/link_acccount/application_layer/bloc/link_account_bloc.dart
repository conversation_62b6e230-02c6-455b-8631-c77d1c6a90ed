import 'package:cbrs/features/link_acccount/application_layer/bloc/link_account_event.dart';
import 'package:cbrs/features/link_acccount/application_layer/bloc/link_account_state.dart';
import 'package:cbrs/features/link_acccount/domain/repositories/link_account_repositories.dart';
import 'package:cbrs/features/link_acccount/domain/usecases/generate_link_account_usecase.dart';
import 'package:cbrs/features/link_acccount/domain/usecases/pended_link_account_usecase.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AccountLinkBloc extends Bloc<AccountLinkEvent, AccountLinkState> {
  AccountLinkBloc({
    required GenerateLinkAccountUsecase generateLinkAccountUsecase,
    required PendedLinkedAccountUseCase pendedLinkedAccountUseCase,
  })  : _generateLinkAccountUsecase = generateLinkAccountUsecase,
        _pendedLinkedAccountUseCase = pendedLinkedAccountUseCase,
        super(AccoutLinkInitialState()) {
    on<FetchLinkedAccountEvent>(handleFetchLinkedAccount);
    on<GenerateLinkCodeEvent>(handleGenerateLinkCode);

    on<ClearLinkAccountEvent>((event, emit) {
      emit(AccountLinkLoadingState());
    });
  }
  // final LinkAccountRepositories _repository;

  final GenerateLinkAccountUsecase _generateLinkAccountUsecase;
  final PendedLinkedAccountUseCase _pendedLinkedAccountUseCase;

  Future<void> handleGenerateLinkCode(
    GenerateLinkCodeEvent event,
    Emitter<AccountLinkState> emit,
  ) async {
    print('handleGenerateLinkCode');

    try {
      emit(AccountLinkLoadingState());
      final result = await _generateLinkAccountUsecase(
        GenerateCodeParams(
          bankID: event.bankID,
          accountNumber: event.accountNumber,
          bankHolderName: event.bankHolderName
        ),
      );
      result.fold(
        (failure) => emit(AccountLinkErrorState(failure.message)),
        (success) => emit(GenerateCodeLoadedState(success)),
      );
    } catch (e) {
      debugPrint(e.toString());
      // final errorMessage = _cleanErrorMessage(e.toString());

      emit(AccountLinkErrorState(e.toString()));
    }
  }

  Future<void> handleFetchLinkedAccount(
    FetchLinkedAccountEvent event,
    Emitter<AccountLinkState> emit,
  ) async {
    print('Fetching handleFetchLinkedAccount...');

    try {
      // Emit loading state
      emit(AccountLinkLoadingState());

      final result = await _pendedLinkedAccountUseCase(
        PendedLinkAccountParams(
          limit: event.limit,
          page: event.page,
          status: event.status,
        ),
      );

      result.fold(
        // On failure,
        (failure) => emit(AccountLinkErrorState(failure.message)),

        // On success,
        (success) => emit(FetchLinkedAccountLoadedState(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      // final errorMessage = _cleanErrorMessage(e.toString());

      emit(AccountLinkErrorState(e.toString()));
    }
  }

// Helper method to clean up error messages
  String _cleanErrorMessage(String error) {
    // Remove generic "Exception: " prefix
    var cleanedError = error.replaceAll('Exception: ', '');

    // for specifc classes like network error, apiexception, serverexception
    if (cleanedError.contains('ApiException(')) {
      final regex = RegExp(r'ApiException\((.*?),');
      final match = regex.firstMatch(cleanedError);
      if (match != null && match.groupCount > 0) {
        cleanedError = match.group(1) ?? cleanedError;
      }
    }

    return cleanedError;
  }
}
