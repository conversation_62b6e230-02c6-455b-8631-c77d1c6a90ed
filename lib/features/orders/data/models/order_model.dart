import '../../domain/entities/order.dart';

class OrderModel extends Order {
  OrderModel({
    required String id,
    required String senderId,
    required String senderName,
    required String beneficiaryName,
    required String beneficiaryPhone,
    required String transactionType,
    required String orderId,
    required String giftPackageId,
    required String giftPackageName,
    required int totalGiftPackageQty,
    required double billAmount,
    required String status,
    required DateTime paidDate,
  }) : super(
          id: id,
          senderId: senderId,
          senderName: senderName,
          beneficiaryName: beneficiaryName,
          beneficiaryPhone: beneficiaryPhone,
          transactionType: transactionType,
          orderId: orderId,
          giftPackageId: giftPackageId,
          giftPackageName: giftPackageName,
          totalGiftPackageQty: totalGiftPackageQty,
          billAmount: billAmount,
          status: status,
          paidDate: paidDate,
        );

factory OrderModel.fromJson(Map<String, dynamic> json) {
  String giftPackageName = '';
  if (json['giftPackage'] != null) {
    // Check if giftPackage is a direct object with a name property
    if (json['giftPackage'] is Map<String, dynamic> && 
        json['giftPackage']['name'] != null) {
      giftPackageName = json['giftPackage']['name'].toString();
    } 
    // Fallback to the old format if needed
    else if (json['giftPackage']['docs'] is List && 
        (json['giftPackage']['docs'] as List).isNotEmpty) {
      giftPackageName = json['giftPackage']['docs'][0]['name']?.toString() ?? '';
    }
  }

  return OrderModel(
    id: json['id']?.toString() ?? '',
    senderId: json['senderId']?.toString() ?? '',
    senderName: json['senderName']?.toString() ?? '',
    beneficiaryName: json['beneficiaryName']?.toString() ?? '',
    beneficiaryPhone: json['beneficiaryPhone']?.toString() ?? '',
    transactionType: json['transactionType']?.toString() ?? '',
    orderId: json['orderId']?.toString() ?? '',
    giftPackageId: json['giftPackageId']?.toString() ?? '',
    giftPackageName: giftPackageName,
    totalGiftPackageQty: json['totalGiftPackageQty'] != null 
        ? int.parse(json['totalGiftPackageQty'].toString()) 
        : 0,
    billAmount: json['billAmount'] != null 
        ? double.parse(json['billAmount'].toString()) 
        : 0.0,
    status: json['status']?.toString() ?? '',
    paidDate: json['paidDate'] != null 
        ? DateTime.parse(json['paidDate'].toString())
        : DateTime.now(),
  );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'beneficiaryName': beneficiaryName,
      'beneficiaryPhone': beneficiaryPhone,
      'transactionType': transactionType,
      'orderId': orderId,
      'giftPackageId': giftPackageId,
      'giftPackageName': giftPackageName,
      'totalGiftPackageQty': totalGiftPackageQty,
      'billAmount': billAmount,
      'status': status,
      'createdAt': paidDate.toIso8601String(),
    };
  }
}
