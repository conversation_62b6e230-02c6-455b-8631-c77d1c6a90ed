import 'package:cbrs/features/orders/domain/entities/order.dart';
import 'package:cbrs/features/orders/domain/entities/order_detail.dart';
import 'package:cbrs/features/orders/domain/entities/order_group.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repositories/order_repository.dart';
import 'order_event.dart';
import 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final OrderRepository repository;

  OrderBloc({required this.repository}) : super(OrderInitial()) {
    on<FetchOrders>(_onFetchOrders);
    on<LoadMoreOrders>(_onLoadMoreOrders);
    on<FetchOrderDetail>(_onFetchOrderDetail);
    on<FetchUnredeemedCount>(_onFetchUnredeemedCount);
    on<GetOrderReceiptEvent>(_onGetOrderReceipt);
  }

  Future<void> _onFetchOrders(
    FetchOrders event,
    Emitter<OrderState> emit,
  ) async {
    try {
      // Only show loading state if we don't have cached data
      final currentState = state;
      if (currentState is! OrderLoaded) {
        emit(OrderLoading());
      }

      final result = await repository.getOrders(
        isBeneficiary: event.isBeneficiary,
        page: event.page,
        limit: event.limit,
        multipleStatus: 'UNREDEEMED,COMPLETED',
      );

      result.fold(
        (failure) => emit(OrderError(message: failure.message)),
        (data) {
          final orders = (data['data']['docs'] as List)
              .map((order) => Order.fromJson(order as Map<String, dynamic>))
              .toList();

          final groupedOrders = _groupOrders(orders);

          emit(OrderLoaded(
            orders: orders,
            groupedOrders: groupedOrders,
            hasReachedMax: !(data['data']['hasNextPage'] as bool),
            currentPage: data['data']['page'] as int,
            totalPages: data['data']['totalPages'] as int,
            isLoadingMore: false,
          ));
        },
      );
    } catch (e) {
      emit(OrderError(message: e.toString()));
    }
  }

  Future<void> _onLoadMoreOrders(
    LoadMoreOrders event,
    Emitter<OrderState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is OrderLoaded) {
        emit(currentState.copyWith(isLoadingMore: true));

        final result = await repository.getOrders(
          isBeneficiary: event.isBeneficiary,
          page: event.page,
          limit: event.limit,
          multipleStatus: 'UNREDEEMED,COMPLETED',
        );

        result.fold(
          (failure) => emit(OrderError(message: failure.message)),
          (data) {
            final newOrders = (data['data']['docs'] as List)
                .map((order) => Order.fromJson(order as Map<String, dynamic>))
                .toList();

            final allOrders = [...currentState.orders, ...newOrders];
            final groupedOrders = _groupOrders(allOrders);

            emit(OrderLoaded(
              orders: allOrders,
              groupedOrders: groupedOrders,
              hasReachedMax: !(data['data']['hasNextPage'] as bool),
              currentPage: data['data']['page'] as int,
              totalPages: data['data']['totalPages'] as int,
              isLoadingMore: false,
            ));
          },
        );
      }
    } catch (e) {
      emit(OrderError(message: e.toString()));
    }
  }

  Future<void> _onFetchOrderDetail(
    FetchOrderDetail event,
    Emitter<OrderState> emit,
  ) async {
    try {
      emit(OrderDetailLoading());

      final orderDetail = await repository.getOrderDetails(event.orderId);

      emit(OrderDetailLoaded(orderDetail: orderDetail));
    } catch (e) {
      emit(OrderDetailError(message: e.toString()));
    }
  }

  Future<void> _onFetchUnredeemedCount(
    FetchUnredeemedCount event,
    Emitter<OrderState> emit,
  ) async {
    try {
      final result = await repository.getUnredeemedCount();

      result.fold(
        (failure) => emit(OrderError(message: failure.message)),
        (count) => emit(OrderCountLoaded(unredeemedCount: count)),
      );
    } catch (e) {
      emit(OrderError(message: e.toString()));
    }
  }

  Future<void> _onGetOrderReceipt(
    GetOrderReceiptEvent event,
    Emitter<OrderState> emit,
  ) async {
    try {
      if (state is OrderDetailLoaded) {
        final currentState = state as OrderDetailLoaded;
        emit(OrderReceiptLoading());

        final result = await repository.getOrderReceipt(event.billRefNo);

        result.fold(
          (failure) {
            emit(OrderError(message: failure.message));
            emit(currentState);
          },
          (receiptUrl) {
            emit(OrderReceiptLoaded(receiptUrl: receiptUrl));
            emit(currentState);
          },
        );
      }
    } catch (e) {
      if (state is OrderDetailLoaded) {
        final currentState = state as OrderDetailLoaded;
        emit(OrderError(message: e.toString()));
        emit(currentState);
      }
    }
  }

  List<OrderGroup> _groupOrders(List<Order> orders) {
    if (orders.isEmpty) {
      return []; // Return empty list if no orders
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    final todayOrders = orders.where((t) {
      final localDate = t.paidDate.toLocal();
      final date = DateTime(localDate.year, localDate.month, localDate.day);
      return date == today;
    }).toList();

    final yesterdayOrders = orders.where((t) {
      final localDate = t.paidDate.toLocal();
      final date = DateTime(localDate.year, localDate.month, localDate.day);
      return date == yesterday;
    }).toList();

    final olderOrders = orders.where((t) {
      final localDate = t.paidDate.toLocal();
      final date = DateTime(localDate.year, localDate.month, localDate.day);
      return date.isBefore(yesterday);
    }).toList();

    final groups = <OrderGroup>[];

    if (todayOrders.isNotEmpty) {
      groups.add(OrderGroup(title: 'Today', orders: todayOrders));
    }

    if (yesterdayOrders.isNotEmpty) {
      groups.add(OrderGroup(title: 'Yesterday', orders: yesterdayOrders));
    }

    if (olderOrders.isNotEmpty) {
      groups.add(OrderGroup(title: 'All Orders', orders: olderOrders));
    }

    // If no groups were created but we had orders, create a default group
    if (groups.isEmpty && orders.isNotEmpty) {
      groups.add(OrderGroup(title: 'All Orders', orders: orders));
    }

    return groups;
  }
}
