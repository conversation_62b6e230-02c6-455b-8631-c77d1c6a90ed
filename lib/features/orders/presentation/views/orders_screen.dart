// main.dart
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/format_date_in_month.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/features/orders/domain/entities/order.dart';
import 'package:cbrs/features/orders/application/blocs/order_bloc.dart';
import 'package:cbrs/features/orders/application/blocs/order_event.dart';
import 'package:cbrs/features/orders/application/blocs/order_state.dart';
import 'package:cbrs/features/orders/presentation/views/received_order_details_screen.dart';
import 'package:cbrs/features/orders/presentation/views/sent_order_details_screen.dart';
import 'package:cbrs/features/transactions/presentation/widgets/transaction_list_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';

// orders_screen.dart
class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  int _selectedTab = 0;
  bool _hasOrders = true;
  final ScrollController _scrollController = ScrollController();
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    _loadOrders();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    // Refresh unredeemed count when leaving the screen
    if (mounted) {
      context.read<OrderBloc>().add(FetchUnredeemedCount());
    }
    super.dispose();
  }

  Future<void> _loadCurrentUser() async {
    final authLocalDataSource = context.read<AuthLocalDataSource>();
    _currentUserId = await authLocalDataSource.getUserId();
    if (mounted) setState(() {});
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.9) {
      final state = context.read<OrderBloc>().state;
      if (state is OrderLoaded &&
          !state.hasReachedMax &&
          !state.isLoadingMore) {
        context.read<OrderBloc>().add(LoadMoreOrders(
              // Only pass isBeneficiary for 'Sent' and 'Received' tabs
              isBeneficiary: _selectedTab == 0 ? null : _selectedTab == 2,
              page: state.currentPage + 1,
              limit: 20, // Increased limit for better pagination
            ));
      }
    }
  }

  void _loadOrders() {
    context.read<OrderBloc>().add(FetchOrders(
          // Only pass isBeneficiary for 'Sent' and 'Received' tabs
          isBeneficiary: _selectedTab == 0 ? null : _selectedTab == 2,
          page: 1,
          limit: 20, // Increased limit for better pagination
        ));
  }

  void _onTap(Order order) async {
    context.read<OrderBloc>().add(FetchOrderDetail(orderId: order.id));

    if (_selectedTab == 2) {
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ReceivedOrderDetailsScreen(orderId: order.id),
        ),
      );
      // Refresh orders when returning from details screen
      if (mounted) _loadOrders();
    } else if (_selectedTab == 1) {
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SentOrderDetailsScreen(orderId: order.id),
        ),
      );
      // Refresh orders when returning from details screen
      if (mounted) _loadOrders();
    } else {
      // For "All" tab, check if the current user is the sender
      final authLocalDataSource = context.read<AuthLocalDataSource>();
      final currentUserId = await authLocalDataSource.getUserId();

      if (!context.mounted) return;

      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => order.senderId == currentUserId
              ? SentOrderDetailsScreen(orderId: order.id)
              : ReceivedOrderDetailsScreen(orderId: order.id),
        ),
      );
      // Refresh orders when returning from details screen
      if (mounted) _loadOrders();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        title: Text(
          'Orders',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Column(
        children: [
          SizedBox(height: 24.h),
          // Tabs
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.w),
            child: Container(
              child: Row(
                children: [
                  _buildTab('All', 0),
                  const SizedBox(width: 8),
                  _buildTab('Sent', 1),
                  const SizedBox(width: 8),
                  _buildTab('Received', 2),
                ],
              ),
            ),
          ),
          Expanded(
            child: BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is OrderLoading) {
                  return const TransactionListShimmer();
                }

                if (state is OrderError) {
                  return Center(child: Text(state.message));
                }

                if (state is OrderLoaded) {
                  if (state.orders.isEmpty) {
                    return _buildEmptyState();
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      _loadOrders();
                    },
                    child: ListView(
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 16.h),
                      children: [
                        if (state.groupedOrders.isNotEmpty)
                          ...state.groupedOrders.map((group) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(bottom: 8.h, top: 8.h),
                                  child: Text(
                                    group.title,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 12.h, horizontal: 12.w),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: group.orders.isNotEmpty 
                                      ? group.orders.map((order) {
                                          return _buildOrderItem(
                                            giftPackageName: order.giftPackageName,
                                            type: order.status,
                                            amount: order.billAmount,
                                            date: order.paidDate,
                                            isReceived: _selectedTab == 2,
                                            senderId: order.senderId,
                                            isLastItem: group.orders.last == order,
                                            onTap: () => _onTap(order),
                                          );
                                        }).toList()
                                      : [const SizedBox()],
                                  ),
                                ),
                                const SizedBox(height: 16),
                              ],
                            );
                          }).toList(),
                        if (state.isLoadingMore)
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.h),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                      ],
                    ),
                  );
                }

                return const SizedBox();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTab == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTab = index;
          });
          _loadOrders(); // Reload orders when tab changes
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color:
                isSelected ? LightModeTheme().primaryColorUSD : const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.white : LightModeTheme().primaryColorUSD,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset('assets/images/empty_orders_img.png',
              width: 120, height: 120),
          const SizedBox(height: 16),
          const Text(
            'No Gifts Yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            "You haven't sent or received any gifts so far.\nOnce you send or receive a gift, it will show up here.",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final List<String> months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: Color(0xFFAAAAAA),
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildOrderItem({
    required String giftPackageName,
    required String type,
    required double amount,
    required DateTime date,
    required bool isReceived,
    required Function()? onTap,
    String? senderId,
    bool isLastItem = false,
  }) {
    final bool isReceivedOrder = _selectedTab == 0
        ? (senderId != null && senderId != _currentUserId)
        : isReceived;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 12.h),
            child: Row(
              children: [
                Container(
                  width: 46.w,
                  height: 46.h,
                  decoration: BoxDecoration(
                    color: LightModeTheme().primaryColorUSD.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(38),
                  ),
                  padding:
                      EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                  child: Image.asset(
                    isReceivedOrder
                        ? MediaRes.recievedOrderIcon
                        : MediaRes.sentGiftCardIcon,
                    color: LightModeTheme().primaryColorUSD.withOpacity(0.6),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              giftPackageName[0].toUpperCase() +
                                  giftPackageName.substring(1),
                              maxLines: 2,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: Colors.black87,
                                    fontSize: 16.sp,
                                  ),
                            ),
                          ),
                          Text(
                            PriceFormatter.formatPrice(
                                amount.toStringAsFixed(2)),
                            style: GoogleFonts.outfit(
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              isReceivedOrder ? 'Received Gift' : 'Sent Gift',
                              style: GoogleFonts.outfit(
                                color: Color(0xFFAAAAAA),
                                fontSize: 14.sp,
                              ),
                            ),
                          ),
                          Text(
                            // date,
                            formatDateToLocal(date.toString()),
                            style: GoogleFonts.outfit(
                              color: Color(0xFFAAAAAA),
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                /*
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        giftPackageName,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                              fontSize: 16.sp,
                            ),
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      Text(
                        isReceivedOrder ? 'Received Gift' : 'Sent Gift',
                        style: GoogleFonts.outfit(
                          color: Color(0xFFAAAAAA),
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      PriceFormatter.formatPrice(amount.toStringAsFixed(2)),
                      style: GoogleFonts.outfit(
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                        fontSize: 16.sp,
                      ),
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    Text(
                      date,
                      style: GoogleFonts.outfit(
                        color: Color(0xFFAAAAAA),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
            
            */
              ],
            ),
          ),
          if (!isLastItem)
            Container(
              // padding: const EdgeInsets.only(top: 6, ),
              height: 1,
              child: CustomPaint(
                painter: DottedLinePainter(
                  color: LightModeTheme().primaryColorUSD.withOpacity(0.2),
                ),
                size: const Size(double.infinity, 1),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Shimmer for section title
                Container(
                  width: 80,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 16),
                // Shimmer for order items container
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children:
                        List.generate(3, (index) => _buildShimmerOrderItem()),
                  ),
                ),
                const SizedBox(height: 24),
                // Second section
                Container(
                  width: 100,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children:
                        List.generate(2, (index) => _buildShimmerOrderItem()),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerOrderItem() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 6),
      child: Row(
        children: [
          // Circle icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(38),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 120,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                width: 60,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 4),
              Container(
                width: 80,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
