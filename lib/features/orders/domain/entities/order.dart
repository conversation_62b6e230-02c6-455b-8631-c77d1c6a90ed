class Order {
  final String id;
  final String senderId;
  final String senderName;
  final String beneficiaryName;
  final String beneficiaryPhone;
  final String transactionType;
  final String orderId;
  final String giftPackageId;
  final String giftPackageName;
  final int totalGiftPackageQty;
  final double billAmount;
  final String status;
  final DateTime paidDate;

  Order({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.beneficiaryName,
    required this.beneficiaryPhone,
    required this.transactionType,
    required this.orderId,
    required this.giftPackageId,
    required this.giftPackageName,
    required this.totalGiftPackageQty,
    required this.billAmount,
    required this.status,
    required this.paidDate,
  });

factory Order.fromJson(Map<String, dynamic> json) {
  // Extract gift package name from nested structure
  String giftPackageName = '';
  if (json['giftPackage'] != null) {
    // Check if giftPackage is a direct object with a name property
    if (json['giftPackage'] is Map<String, dynamic> && 
        json['giftPackage']['name'] != null) {
      giftPackageName = json['giftPackage']['name'].toString();
    } 
    // Fallback to the old format if needed
    else if (json['giftPackage']['docs'] is List && 
        (json['giftPackage']['docs'] as List).isNotEmpty) {
      giftPackageName = json['giftPackage']['docs'][0]['name']?.toString() ?? '';
    }
  }
  
  return Order(
    id: json['id']?.toString() ?? '',
    senderId: json['senderId']?.toString() ?? '',
    senderName: json['senderName']?.toString() ?? '',
    beneficiaryName: json['beneficiaryName']?.toString() ?? '',
    beneficiaryPhone: json['beneficiaryPhone']?.toString() ?? '',
    transactionType: json['transactionType']?.toString() ?? '',
    orderId: json['orderId']?.toString() ?? '',
    giftPackageId: json['giftPackageId']?.toString() ?? '',
    totalGiftPackageQty: json['totalGiftPackageQty'] != null 
        ? int.parse(json['totalGiftPackageQty'].toString()) 
        : 0,
    billAmount: json['billAmount'] != null 
        ? double.parse(json['billAmount'].toString()) 
        : 0.0,
    giftPackageName: giftPackageName,
    status: json['status']?.toString() ?? '',
    paidDate: json['paidDate'] != null 
        ? DateTime.parse(json['createdAt'].toString())
        : DateTime.now(),
  );
}
}
