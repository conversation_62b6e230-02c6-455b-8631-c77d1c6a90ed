import 'package:cbrs/features/chat/domain/entities/chat_message.dart';
import 'package:cbrs/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:cbrs/features/chat/presentation/cubit/message_selection_cubit.dart';
import 'package:cbrs/features/chat/presentation/widgets/multi_message_forward_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:hugeicons/hugeicons.dart';

/// App bar for message selection mode
class MessageSelectionAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const MessageSelectionAppBar({
    required this.onExitSelection,
    required this.conversationId,
    super.key,
  });

  final VoidCallback onExitSelection;
  final String conversationId;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MessageSelectionCubit, MessageSelectionState>(
      builder: (context, selectionState) {
        return AppBar(
          // Keep the same background and foreground colors as normal app bar
          backgroundColor: Theme.of(context).colorScheme.surface,
          foregroundColor: Theme.of(context).colorScheme.onSurface,
          elevation: .3,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: onExitSelection,
          ),
          title: Text(
            '${selectionState.selectedMessages.length} selected',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          actions: [
            // Copy action (only show if there are text messages)
            if (selectionState.selectedMessages.isNotEmpty &&
                selectionState.selectedMessages
                    .any((m) => m.messageType == ChatMessageType.text))
              IconButton(
                icon: const Icon(FluentIcons.copy_20_regular),
                onPressed: () => _handleCopyMessages(
                  context,
                  selectionState.selectedMessages,
                ),
                tooltip: 'Copy text',
              ),

            // // Edit action (only show if single text message is selected)
            // if (selectionState.selectedMessages.length == 1 &&
            //     selectionState.selectedMessages.first.messageType ==
            //         ChatMessageType.text)
            //   IconButton(
            //     icon: const Icon(FluentIcons.edit_20_regular),
            //     onPressed: () => _handleEditMessage(
            //       context,
            //       selectionState.selectedMessages.first,
            //     ),
            //     tooltip: 'Edit message',
            //   ),

            // Forward action
            if (selectionState.selectedMessages.isNotEmpty)
              IconButton(
                icon: const Icon(CupertinoIcons.arrow_up_right),
                onPressed: () async => _handleForwardMessages(
                  context,
                  selectionState.selectedMessages,
                ),
                tooltip: 'Forward messages',
              ),

            // Delete action (only show if there are deletable messages)
            if (selectionState.selectedMessages.isNotEmpty)
              IconButton(
                icon: const Icon(FluentIcons.delete_20_regular),
                onPressed: () => _handleDeleteMessages(
                  context,
                  selectionState.selectedMessages,
                  conversationId,
                ),
                tooltip: 'Delete messages',
              ),

            // More actions
            if (selectionState.selectedMessages.isNotEmpty)
              PopupMenuButton<String>(
                icon: const Icon(FluentIcons.more_vertical_20_regular),
                onSelected: (value) => _handleMenuAction(
                  context,
                  value,
                  selectionState.selectedMessages,
                ),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'select_all',
                    child: Row(
                      children: [
                        Icon(FluentIcons.select_all_on_20_regular),
                        SizedBox(width: 12),
                        Text('Select All'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_selection',
                    child: Row(
                      children: [
                        Icon(FluentIcons.dismiss_20_regular),
                        SizedBox(width: 12),
                        Text('Clear Selection'),
                      ],
                    ),
                  ),
                ],
              ),
          ],
        );
      },
    );
  }

  void _handleCopyMessages(
    BuildContext context,
    List<ChatMessageEntity> messages,
  ) {
    final textMessages =
        messages.where((m) => m.messageType == ChatMessageType.text).toList();

    if (textMessages.isEmpty) return;

    // Sort by timestamp to maintain chronological order
    textMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Combine text content
    final combinedText = textMessages.map((m) => m.content).join('\n\n');

    // Copy to clipboard
    Clipboard.setData(ClipboardData(text: combinedText));

    // Trigger the copy event in the bloc
    context.read<ChatBloc>().add(CopySelectedMessagesEvent(messages: messages));

    // Exit selection mode
    onExitSelection();
  }

  Future<void> _handleForwardMessages(
    BuildContext context,
    List<ChatMessageEntity> messages,
  ) async {
    if (messages.isEmpty) return;

    try {
      // Use the enhanced forward message system for multiple messages
      await showMultiMessageForwardSheet(
        context: context,
        messages: messages,
      );

      // Exit selection mode after successful forward
      onExitSelection();
    } catch (e) {
      onExitSelection();
    }
  }

  void _handleDeleteMessages(
    BuildContext context,
    List<ChatMessageEntity> messages,
    String conversationId,
  ) {
    // Show iOS-style confirmation dialog
    showCupertinoDialog<void>(
      context: context,
      builder: (dialogContext) => CupertinoAlertDialog(
        title: const Text('Delete Messages'),
        content: Text(
          'Are you sure you want to delete ${messages.length} message(s)? '
          'This action cannot be undone.',
        ),
        actions: [
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(dialogContext);

              // Trigger delete event in the bloc
              context.read<ChatBloc>().add(
                    DeleteSelectedMessagesEvent(
                      messageIds: messages.map((m) => m.id).toList(),
                      conversationId: conversationId,
                    ),
                  );

              onExitSelection();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(
    BuildContext context,
    String action,
    List<ChatMessageEntity> messages,
  ) {
    final selectionCubit = context.read<MessageSelectionCubit>();
    final chatBloc = context.read<ChatBloc>();

    switch (action) {
      case 'select_all':
        // Get all messages from the chat state
        final allMessages = chatBloc.state.messages;
        selectionCubit.selectAll(allMessages);

      case 'clear_selection':
        // Clear selections and exit selection mode
        selectionCubit.clearSelections();
        onExitSelection();
    }
  }

  void _handleEditMessage(
    BuildContext context,
    ChatMessageEntity message,
  ) {
    // Show iOS-style edit dialog
    final TextEditingController controller =
        TextEditingController(text: message.content);

    showCupertinoDialog<void>(
      context: context,
      builder: (dialogContext) => CupertinoAlertDialog(
        title: const Text('Edit Message'),
        content: Padding(
          padding: const EdgeInsets.only(top: 16),
          child: CupertinoTextField(
            controller: controller,
            placeholder: 'Enter message',
            maxLines: 3,
            minLines: 1,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: CupertinoColors.systemGrey4),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        actions: [
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              final newContent = controller.text.trim();
              if (newContent.isNotEmpty && newContent != message.content) {
                Navigator.pop(dialogContext);

                // Trigger edit event in the bloc
                context.read<ChatBloc>().add(
                      EditMessage(
                        messageId: message.id,
                        newContent: newContent,
                      ),
                    );

                onExitSelection();
              } else {
                Navigator.pop(dialogContext);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
