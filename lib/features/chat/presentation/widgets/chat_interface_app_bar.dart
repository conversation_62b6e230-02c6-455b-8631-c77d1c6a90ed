import 'dart:async';

import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/chat/domain/entities/conversation.dart';
import 'package:cbrs/features/chat/domain/entities/user_attributes.dart';
import 'package:cbrs/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:cbrs/features/chat/presentation/cubit/chat_app_bar_cubit.dart';
import 'package:cbrs/features/chat/presentation/pages/chat_search_page.dart';
import 'package:cbrs/features/chat/presentation/widgets/ios_style_menu_button.dart';
import 'package:cbrs/features/chat/presentation/widgets/presence_status_indicator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChatInterfaceAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const ChatInterfaceAppBar({
    super.key,
    this.conversation,
    this.userAttributes,
    this.isAdmin = false,
  });

  final ConversationEntity? conversation;
  final UserAttributesEntity? userAttributes;
  final bool isAdmin;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.pop(context),
      ),
      title: _buildTitle(context),
      actions: [
        BlocBuilder<ChatBloc, ChatState>(
          buildWhen: (previous, current) =>
              previous.contacts != current.contacts ||
              previous.contactsStatus != current.contactsStatus ||
              previous.conversationsStatus != current.conversationsStatus ||
              previous.blockingUser != current.blockingUser,
          builder: (context, state) {
            return IOSStyleMenuButton(
              icon: const Icon(Icons.more_vert),
              items: _buildMenuItems(context, state),
            );
          },
        ),
      ],
      elevation: .3,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  /// Build title widget using passed user attributes or fallback to cubit
  Widget _buildTitle(BuildContext context) {
    // If we have user attributes, use them directly - no complex logic needed!
    if (userAttributes != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            userAttributes!.displayName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          // Show email as subtitle (preferred over phone number)
          if (userAttributes!.email?.isNotEmpty == true)
            Text(
              userAttributes!.email!,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            )
          // Fallback to phone number if email is not available
          else if (userAttributes!.phoneNumber?.isNotEmpty == true)
            Text(
              userAttributes!.phoneNumber!,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
        ],
      );
    }

    // Fallback to cubit for cases where user attributes aren't available
    return BlocBuilder<ChatAppBarCubit, ChatAppBarState>(
      buildWhen: (previous, current) =>
          previous.title != current.title ||
          previous.subtitle != current.subtitle,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              state.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (state.subtitle != null)
              AppBarPresenceIndicator(
                isOnline: state.isOnline,
                subtitle: state.subtitle,
              ),
          ],
        );
      },
    );
  }

  List<IOSStyleMenuItem> _buildMenuItems(
    BuildContext context,
    ChatState state,
  ) {
    final items = <IOSStyleMenuItem>[];

    // Add search option if we have a conversation
    if (conversation != null) {
      items.add(
        IOSStyleMenuItem(
          title: 'Search Messages',
          icon: CupertinoIcons.search,
          onTap: () {
            _handleMenuAction(context, 'search');
          },
        ),
      );

      // Add clear chat option
      items.add(
        IOSStyleMenuItem(
          title: 'Clear Chat',
          icon: CupertinoIcons.delete,
          isDestructive: true,
          onTap: () {
            _handleMenuAction(context, 'clear');
          },
        ),
      );

      // Add block/unblock user option for direct messages
      if (conversation!.isDirectMessage &&
          state.contactsStatus == ChatStatus.success &&
          state.conversationsStatus == ChatStatus.success) {
        // Use the same logic as isConversationContactBlocked
        // to find the contact
        final isBlocked = state.isConversationContactBlocked(
          conversation!.id,
        );
        final isBlockingInProgress = state.blockingUser;

        items.add(
          IOSStyleMenuItem(
            title: isBlockingInProgress
                ? (isBlocked ? 'Unblocking...' : 'Blocking...')
                : (isBlocked ? 'Unblock User' : 'Block User'),
            icon: isBlocked
                ? CupertinoIcons.person_crop_circle_badge_checkmark
                : CupertinoIcons.person_crop_circle_badge_xmark,
            isDestructive: !isBlocked && !isBlockingInProgress,
            onTap: isBlockingInProgress
                ? () {}
                : () {
                    _handleMenuAction(
                      context,
                      isBlocked ? 'unblock_user' : 'block_user',
                    );
                  },
          ),
        );
      }
    } else {
      // Add disabled search option if no conversation
      items.add(
        IOSStyleMenuItem(
          title: 'Search Messages',
          icon: CupertinoIcons.search,
          onTap: () {
            CustomToastification(
              context,
              message: 'Cannot search: no active conversation',
            );
          },
        ),
      );
    }

    // Add admin options if applicable
    if (conversation != null && isAdmin) {
      items.add(
        IOSStyleMenuItem(
          title: 'Manage Members',
          icon: CupertinoIcons.person_2,
          onTap: () => _handleMenuAction(context, 'members'),
        ),
      );

      items.add(
        IOSStyleMenuItem(
          title: 'Mute All',
          icon: CupertinoIcons.volume_off,
          onTap: () => _handleMenuAction(context, 'mute_all'),
        ),
      );

      items.add(
        IOSStyleMenuItem(
          title: 'Update Announcement',
          icon: CupertinoIcons.speaker_2,
          onTap: () => _handleMenuAction(context, 'announcement'),
        ),
      );
    }

    return items;
  }

  void _handleMenuAction(BuildContext context, String value) {
    switch (value) {
      case 'search':
        _showSearchDialog(context);
      case 'clear':
        _showClearChatDialog(context);
      case 'block_user':
        _showBlockUserDialog(context);
      case 'unblock_user':
        _showUnblockUserDialog(context);
      case 'members':
        _showMembersDialog(context);
      case 'mute_all':
        _showMuteAllDialog(context);
      case 'announcement':
        _showAnnouncementDialog(context);
    }
  }

  void _showClearChatDialog(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (dialogContext) => CupertinoAlertDialog(
        title: const Text('Clear Chat'),
        content: const Text('Are you sure you want to clear all messages?'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.pop(dialogContext),
            isDefaultAction: true,
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(dialogContext);
              if (conversation != null) {
                try {
                  final chatBloc = context.read<ChatBloc>();

                  late StreamSubscription subscription;
                  subscription = chatBloc.stream.listen((state) {
                    if (context.mounted) {
                      Navigator.of(context, rootNavigator: true).pop();
                      subscription.cancel();

                      CustomToastification(
                        context,
                        message: 'Chat cleared successfully!',
                        isError: false,
                      );
                    }
                  });

                  showCupertinoDialog<void>(
                    context: context,
                    barrierDismissible: false,
                    builder: (loadingContext) => const CupertinoAlertDialog(
                      title: Text('Clearing Chat'),
                      content: Padding(
                        padding: EdgeInsets.symmetric(vertical: 20),
                        child: Center(
                          child: CupertinoActivityIndicator(),
                        ),
                      ),
                    ),
                  );

                  // Dispatch the clear chat event
                  chatBloc
                      .add(ClearChatEvent(conversationId: conversation!.id));
                } catch (e) {
                  if (context.mounted) {
                    Navigator.of(context, rootNavigator: true)
                        .pop(); // Dismiss loading
                    CustomToastification(
                      context,
                      message: 'Failed to clear chat: $e',
                      isError: true,
                    );
                  }
                }
              }
            },
            isDestructiveAction: true,
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    if (conversation != null) {
      // Use a simpler navigation approach to avoid context issues
      Navigator.push(
        context,
        MaterialPageRoute<void>(
          builder: (_) => ChatSearchPage(conversationId: conversation!.id),
        ),
      );
    } else {
      CustomToastification(
        context,
        message: 'Cannot search: no active conversation',
      );
    }
  }

  void _showMembersDialog(BuildContext context) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Manage Members'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Implement add member
            },
            child: const Text('Add Member'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Implement remove member
            },
            child: const Text('Remove Member'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  void _showMuteAllDialog(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Mute All Members'),
        content: const Text('Are you sure you want to mute all members?'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.pop(context),
            isDefaultAction: true,
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              // Implement mute all
              Navigator.pop(context);
            },
            child: const Text('Mute All'),
          ),
        ],
      ),
    );
  }

  void _showBlockUserDialog(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (dialogContext) => CupertinoAlertDialog(
        title: const Text('Block User'),
        content: const Text(
          'Are you sure you want to block this user? You will no longer '
          'receive messages from them.',
        ),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.pop(dialogContext),
            isDefaultAction: true,
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(dialogContext);
              if (conversation != null && conversation!.isDirectMessage) {
                try {
                  // Get the ChatBloc instance from the context
                  final chatBloc = context.read<ChatBloc>();

                  chatBloc.add(
                    BlockUserEvent(
                      conversationId: conversation!.id,
                      participantId: userAttributes?.userId,
                      participantUsername: userAttributes?.agoraUsername,
                    ),
                  );

                  CustomToastification(
                    context,
                    message: 'User ${userAttributes?.displayName ?? 'contact'} '
                        'has been blocked',
                    isError: false,
                  );
                } catch (e) {
                  // Show error dialog
                  showCupertinoDialog<void>(
                    context: context,
                    builder: (errorContext) => CupertinoAlertDialog(
                      title: const Text('Error'),
                      content: Text('Failed to block user: $e'),
                      actions: [
                        CupertinoDialogAction(
                          onPressed: () => Navigator.pop(errorContext),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                }
              }
            },
            isDestructiveAction: true,
            child: const Text('Block'),
          ),
        ],
      ),
    );
  }

  void _showUnblockUserDialog(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (dialogContext) => CupertinoAlertDialog(
        title: const Text('Unblock User'),
        content: const Text(
          'Are you sure you want to unblock this user? You will start '
          'receiving messages from them again.',
        ),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.pop(dialogContext),
            isDefaultAction: true,
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(dialogContext);
              if (conversation != null && conversation!.isDirectMessage) {
                try {
                  // Get the ChatBloc instance from the context
                  final chatBloc = context.read<ChatBloc>();

                  // Dispatch the unblock user event with user attributes
                  chatBloc.add(
                    UnblockUserEvent(
                      conversationId: conversation!.id,
                      participantId: userAttributes?.userId,
                      participantUsername: userAttributes?.agoraUsername,
                    ),
                  );

                  // Show success feedback
                  CustomToastification(
                    context,
                    message: 'User ${userAttributes?.displayName ?? 'contact'} '
                        'has been unblocked',
                    isError: false,
                  );
                } catch (e) {
                  // Show error dialog
                  showCupertinoDialog<void>(
                    context: context,
                    builder: (errorContext) => CupertinoAlertDialog(
                      title: const Text('Error'),
                      content: Text('Failed to unblock user: $e'),
                      actions: [
                        CupertinoDialogAction(
                          onPressed: () => Navigator.pop(errorContext),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                }
              }
            },
            child: const Text('Unblock'),
          ),
        ],
      ),
    );
  }

  void _showAnnouncementDialog(BuildContext context) {
    final controller = TextEditingController();
    showCupertinoModalPopup<void>(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: CupertinoActionSheet(
          title: const Text('Update Announcement'),
          message: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: CupertinoTextField(
              controller: controller,
              placeholder: 'Enter announcement',
              maxLines: 3,
              padding: const EdgeInsets.all(12),
            ),
          ),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  // Implement announcement update
                  Navigator.pop(context);
                }
              },
              child: const Text('Update'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
      ),
    );
  }
}
